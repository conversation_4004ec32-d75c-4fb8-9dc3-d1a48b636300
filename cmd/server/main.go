package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"crm-backend/internal/adapters/database"
	"crm-backend/internal/adapters/database/postgres"
	dbRepos "crm-backend/internal/adapters/database/repositories"
	"crm-backend/internal/adapters/events"
	"crm-backend/internal/adapters/http/handlers"
	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/adapters/webhook"
	"crm-backend/internal/application/usecases"
	"crm-backend/internal/domain/repositories"
	"crm-backend/internal/infrastructure/config"
	"crm-backend/internal/infrastructure/redis"
	legacyUsecases "crm-backend/internal/usecases"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"gorm.io/gorm"
)

// App holds application dependencies
type App struct {
	config      *config.Config
	db          *gorm.DB
	redisClient *redis.Client
	fiber       *fiber.App
	
	// Services
	sseManager     *events.SSEManager
	webhookService *webhook.WebhookService
	eventBus       legacyUsecases.EventBus
	
	// Repositories
	userRepo          repositories.UserRepository
	cardRepo          repositories.CardRepository
	stageRepo         repositories.StageRepository
	activityRepo      repositories.ActivityRepository
	pipelineRepo      repositories.PipelineRepository
	contactRepo       repositories.ContactRepository
	companyRepo       repositories.CompanyRepository
	roleRepo          repositories.RoleRepository
	permissionRepo    repositories.PermissionRepository
	commentRepo       repositories.CommentRepository
	dataTransferRepo  repositories.DataTransferRepository
	
	// Use cases
	authUsecase            *legacyUsecases.AuthUsecase
	cardUsecase            *legacyUsecases.CardUsecase
	pipelineUsecase        *legacyUsecases.PipelineUsecase
	contactUsecase         *legacyUsecases.ContactUsecase
	companyUsecase         *legacyUsecases.CompanyUsecase
	roleUsecase            *usecases.RoleUsecase
	userManagementUsecase  *usecases.UserManagementUsecase
	commentUsecase         *legacyUsecases.CommentUseCase
	dataTransferUsecase    *legacyUsecases.DataTransferUseCase
	
	// Handlers
	authHandler           *handlers.AuthHandler
	cardHandler           *handlers.CardHandler
	fileHandler           *handlers.FileHandler
	pipelineHandler       *handlers.PipelineHandler
	dashboardHandler      *handlers.DashboardHandler
	contactHandler        *handlers.ContactHandler
	companyHandler        *handlers.CompanyHandler
	roleHandler           *handlers.RoleHandler
	userManagementHandler *handlers.UserManagementHandler
	commentHandler        *handlers.CommentHandler
	dataTransferHandler   *handlers.DataTransferHandler
	
	// Middleware
	authMiddleware *middleware.AuthMiddleware
}

func main() {
	if err := run(); err != nil {
		log.Fatal(err)
	}
}

func run() error {
	// Load configuration
	cfg, err := config.LoadConfig(".")
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}
	
	// Initialize application
	app, err := initializeApp(cfg)
	if err != nil {
		return fmt.Errorf("failed to initialize app: %w", err)
	}
	
	// Setup routes
	app.setupRoutes()
	
	// Start background workers
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	
	app.startBackgroundWorkers(ctx)
	
	// Start server
	go func() {
		addr := cfg.Server.Host + ":" + cfg.Server.Port
		log.Printf("🚀 Server starting on %s", addr)
		if err := app.fiber.Listen(addr); err != nil {
			log.Printf("Server error: %v", err)
		}
	}()
	
	// Wait for interrupt signal
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c
	
	log.Println("🔄 Shutting down server...")
	
	// Graceful shutdown
	cancel() // Cancel background workers
	
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()
	
	if err := app.fiber.ShutdownWithContext(shutdownCtx); err != nil {
		return fmt.Errorf("server shutdown error: %w", err)
	}
	
	// Stop SSE manager
	app.sseManager.Stop()
	
	log.Println("✅ Server stopped")
	return nil
}

// initializeApp initializes the application with all dependencies
func initializeApp(cfg *config.Config) (*App, error) {
	// Initialize database
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}
	
	// Run migrations
	if err := database.AutoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}
	
	// Seed initial data
	if err := database.SeedData(db); err != nil {
		return nil, fmt.Errorf("failed to seed data: %w", err)
	}
	
	// Initialize Redis client
	redisClient, err := redis.NewClient(&cfg.Redis)
	if err != nil {
		// Log error but continue - Redis is optional for now
		log.Printf("⚠️  Warning: Failed to connect to Redis: %v", err)
		log.Printf("    Token blacklisting and caching will be disabled")
	}
	
	// Initialize repositories
	userRepo := dbRepos.NewUserRepository(db)
	cardRepo := dbRepos.NewCardRepository(db)
	stageRepo := dbRepos.NewStageRepository(db)
	activityRepo := dbRepos.NewActivityRepository(db)
	pipelineRepo := dbRepos.NewPipelineRepository(db)
	contactRepo := dbRepos.NewContactRepository(db)
	companyRepo := dbRepos.NewCompanyRepository(db)
	roleRepo := postgres.NewRoleRepository(db)
	permissionRepo := postgres.NewPermissionRepository(db)
	commentRepo := dbRepos.NewCommentRepository(db)
	dataTransferRepo := dbRepos.NewDataTransferRepository(db)
	
	// Initialize SSE manager
	sseManager := events.NewSSEManager()
	sseManager.Start()
	
	// Initialize event bus
	eventBus := events.NewEventBus(sseManager)
	
	// Initialize webhook service
	// Note: We would need webhook repository implementations
	// webhookRepo := dbRepos.NewWebhookRepository(db)
	// webhookDeliveryRepo := dbRepos.NewWebhookDeliveryRepository(db)
	// webhookService := webhook.NewWebhookService(webhookRepo, webhookDeliveryRepo)
	
	// For now, create a placeholder
	var webhookService *webhook.WebhookService
	
	// Initialize use cases
	var authUsecase *legacyUsecases.AuthUsecase
	if redisClient != nil {
		authUsecase = legacyUsecases.NewAuthUsecaseWithRedis(userRepo, cfg.Auth.JWTSecret, redisClient)
	} else {
		authUsecase = legacyUsecases.NewAuthUsecase(userRepo, cfg.Auth.JWTSecret)
	}
	cardUsecase := legacyUsecases.NewCardUsecase(cardRepo, stageRepo, activityRepo, eventBus)
	pipelineUsecase := legacyUsecases.NewPipelineUsecase(pipelineRepo, stageRepo, eventBus)
	contactUsecase := legacyUsecases.NewContactUsecase(contactRepo, activityRepo, eventBus)
	companyUsecase := legacyUsecases.NewCompanyUsecase(companyRepo, contactRepo, activityRepo, eventBus)
	roleUsecase := usecases.NewRoleUsecase(roleRepo, permissionRepo, userRepo)
	userManagementUsecase := usecases.NewUserManagementUsecase(userRepo, roleRepo)
	commentUsecase := legacyUsecases.NewCommentUseCase(commentRepo, cardRepo, contactRepo, companyRepo, userRepo, activityRepo)
	dataTransferUsecase := legacyUsecases.NewDataTransferUseCase(dataTransferRepo, cardRepo, contactRepo, companyRepo, userRepo, cfg.Storage.UploadPath)
	
	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(authUsecase)
	
	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authUsecase)
	cardHandler := handlers.NewCardHandler(cardUsecase)
	fileHandler := handlers.NewFileHandler(db, &cfg.Storage)
	pipelineHandler := handlers.NewPipelineHandler(pipelineUsecase)
	dashboardHandler := handlers.NewDashboardHandler(cardUsecase, pipelineUsecase, db, redisClient)
	contactHandler := handlers.NewContactHandler(contactUsecase)
	companyHandler := handlers.NewCompanyHandler(companyUsecase)
	roleHandler := handlers.NewRoleHandler(roleUsecase)
	userManagementHandler := handlers.NewUserManagementHandler(userManagementUsecase, roleUsecase)
	commentHandler := handlers.NewCommentHandler(commentUsecase, sseManager)
	dataTransferHandler := handlers.NewDataTransferHandler(dataTransferUsecase, sseManager, cfg.Storage.UploadPath)
	
	// Initialize Fiber app
	fiberApp := fiber.New(fiber.Config{
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}
			
			return c.Status(code).JSON(handlers.ErrorResponse{
				Error: err.Error(),
			})
		},
	})
	
	return &App{
		config:         cfg,
		db:             db,
		redisClient:    redisClient,
		fiber:          fiberApp,
		sseManager:     sseManager,
		webhookService: webhookService,
		eventBus:       eventBus,
		userRepo:              userRepo,
		cardRepo:              cardRepo,
		stageRepo:             stageRepo,
		activityRepo:          activityRepo,
		pipelineRepo:          pipelineRepo,
		contactRepo:           contactRepo,
		companyRepo:           companyRepo,
		roleRepo:              roleRepo,
		permissionRepo:        permissionRepo,
		commentRepo:           commentRepo,
		dataTransferRepo:      dataTransferRepo,
		authUsecase:           authUsecase,
		cardUsecase:           cardUsecase,
		pipelineUsecase:       pipelineUsecase,
		contactUsecase:        contactUsecase,
		companyUsecase:        companyUsecase,
		roleUsecase:           roleUsecase,
		userManagementUsecase: userManagementUsecase,
		commentUsecase:        commentUsecase,
		dataTransferUsecase:   dataTransferUsecase,
		authHandler:           authHandler,
		cardHandler:           cardHandler,
		fileHandler:           fileHandler,
		pipelineHandler:       pipelineHandler,
		dashboardHandler:      dashboardHandler,
		contactHandler:        contactHandler,
		companyHandler:        companyHandler,
		roleHandler:           roleHandler,
		userManagementHandler: userManagementHandler,
		commentHandler:        commentHandler,
		dataTransferHandler:   dataTransferHandler,
		authMiddleware:        authMiddleware,
	}, nil
}

// setupRoutes sets up all application routes
func (app *App) setupRoutes() {
	// Global middleware
	app.fiber.Use(middleware.Logger())
	app.fiber.Use(middleware.SecurityHeaders()) // Add security headers
	app.fiber.Use(middleware.RequestValidator()) // Input validation
	app.fiber.Use(middleware.CORS())
	app.fiber.Use(recover.New())
	app.fiber.Use(middleware.RateLimit())
	app.fiber.Use(middleware.CSRF()) // CSRF protection for state-changing operations
	
	// Health check
	app.fiber.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":    "ok",
			"timestamp": time.Now(),
			"version":   "1.0.0",
		})
	})
	
	// API routes
	api := app.fiber.Group("/api/v1")
	api.Use(middleware.APIRateLimit())
	
	// Auth routes (public)
	auth := api.Group("/auth")
	auth.Post("/login", app.authHandler.Login)
	auth.Post("/register", app.authHandler.Register)
	
	// Protected auth routes
	authProtected := auth.Group("", app.authMiddleware.RequireAuth())
	authProtected.Post("/refresh", app.authHandler.RefreshToken)
	authProtected.Get("/profile", app.authHandler.GetProfile)
	authProtected.Post("/logout", app.authHandler.Logout)
	
	// Card routes (protected)
	if app.cardHandler != nil {
		cards := api.Group("/cards", app.authMiddleware.RequireAuth())
		cards.Post("/", app.cardHandler.CreateCard)
		cards.Get("/", app.cardHandler.ListCards)
		cards.Get("/overdue", app.cardHandler.GetOverdueCards)
		cards.Get("/:id", app.cardHandler.GetCard)
		cards.Put("/:id", app.cardHandler.UpdateCard)
		cards.Delete("/:id", app.cardHandler.DeleteCard)
		cards.Patch("/:id/move/:stage_id", app.cardHandler.MoveCardToStage)
		cards.Patch("/:id/custom-fields", app.cardHandler.UpdateCustomFields)
	}

	// Pipeline routes (protected)
	if app.pipelineHandler != nil {
		pipelines := api.Group("/pipelines", app.authMiddleware.RequireAuth())
		pipelines.Get("/", app.pipelineHandler.ListPipelines)
		pipelines.Get("/default", app.pipelineHandler.GetDefaultPipeline)
		pipelines.Post("/", app.pipelineHandler.CreatePipeline)
		pipelines.Get("/:id", app.pipelineHandler.GetPipeline)
		pipelines.Put("/:id", app.pipelineHandler.UpdatePipeline)
		pipelines.Delete("/:id", app.pipelineHandler.DeletePipeline)
		pipelines.Patch("/:id/default", app.pipelineHandler.SetDefaultPipeline)
		pipelines.Get("/:id/stages", app.pipelineHandler.GetPipelineStages)
		pipelines.Post("/:id/stages", app.pipelineHandler.CreateStage)
		pipelines.Patch("/:id/stages/reorder", app.pipelineHandler.ReorderStages)
		pipelines.Patch("/:id/settings", app.pipelineHandler.UpdatePipelineSettings)
		
		// Stage routes
		stages := api.Group("/stages", app.authMiddleware.RequireAuth())
		stages.Put("/:id", app.pipelineHandler.UpdateStage)
		stages.Delete("/:id", app.pipelineHandler.DeleteStage)
	}

	// Dashboard routes (protected)
	if app.dashboardHandler != nil {
		dashboard := api.Group("/dashboard", app.authMiddleware.RequireAuth())
		dashboard.Get("/stats", app.dashboardHandler.GetDashboardStats)
	}
	
	// File routes (protected)
	files := api.Group("/files", app.authMiddleware.RequireAuth())
	files.Post("/upload", app.fileHandler.UploadFile)
	files.Get("/", app.fileHandler.ListFiles)
	files.Get("/:id", app.fileHandler.DownloadFile)
	files.Get("/:id/info", app.fileHandler.GetFileInfo)
	files.Delete("/:id", app.fileHandler.DeleteFile)
	
	// Real-time endpoints
	realtime := api.Group("/realtime")
	realtime.Get("/events", app.authMiddleware.RequireAuth(), app.sseManager.SSEHandler())
	realtime.Get("/ws", app.sseManager.WebSocketHandler())

	// Contact routes (protected)
	if app.contactHandler != nil {
		contacts := api.Group("/contacts", app.authMiddleware.RequireAuth())
		contacts.Post("/", app.contactHandler.CreateContact)
		contacts.Get("/", app.contactHandler.ListContacts)
		contacts.Get("/search", app.contactHandler.SearchContacts)
		contacts.Get("/:id", app.contactHandler.GetContact)
		contacts.Put("/:id", app.contactHandler.UpdateContact)
		contacts.Delete("/:id", app.contactHandler.DeleteContact)
		contacts.Patch("/:id/custom-fields", app.contactHandler.UpdateCustomFields)
	}

	// Company routes (protected)
	if app.companyHandler != nil {
		companies := api.Group("/companies", app.authMiddleware.RequireAuth())
		companies.Post("/", app.companyHandler.CreateCompany)
		companies.Get("/", app.companyHandler.ListCompanies)
		companies.Get("/search", app.companyHandler.SearchCompanies)
		companies.Get("/industry/:industry", app.companyHandler.GetCompaniesByIndustry)
		companies.Get("/:id", app.companyHandler.GetCompany)
		companies.Put("/:id", app.companyHandler.UpdateCompany)
		companies.Delete("/:id", app.companyHandler.DeleteCompany)
		companies.Patch("/:id/custom-fields", app.companyHandler.UpdateCustomFields)
		companies.Get("/:id/contacts", app.companyHandler.GetCompanyContacts)
	}

	// Role routes (protected)
	if app.roleHandler != nil {
		roles := api.Group("/roles", app.authMiddleware.RequireAuth())
		roles.Post("/", app.roleHandler.CreateRole)
		roles.Get("/", app.roleHandler.ListRoles)
		roles.Get("/:id", app.roleHandler.GetRole)
		roles.Put("/:id", app.roleHandler.UpdateRole)
		roles.Delete("/:id", app.roleHandler.DeleteRole)
		roles.Post("/:id/permissions", app.roleHandler.AssignPermissions)
		roles.Get("/:id/permissions", app.roleHandler.GetRolePermissions)
		roles.Post("/:id/duplicate", app.roleHandler.DuplicateRole)
		
		// Permission routes
		permissions := api.Group("/permissions", app.authMiddleware.RequireAuth())
		permissions.Get("/", app.roleHandler.GetAllPermissions)
		permissions.Get("/resource/:resource", app.roleHandler.GetPermissionsByResource)
	} else {
		log.Println("WARNING: roleHandler is nil, routes not registered")
	}

	// Comment routes (protected)
	if app.commentHandler != nil {
		app.commentHandler.RegisterRoutes(api.Group("", app.authMiddleware.RequireAuth()))
	} else {
		log.Println("WARNING: commentHandler is nil, routes not registered")
	}

	// Data transfer routes (protected)
	if app.dataTransferHandler != nil {
		app.dataTransferHandler.RegisterRoutes(api.Group("", app.authMiddleware.RequireAuth()))
	} else {
		log.Println("WARNING: dataTransferHandler is nil, routes not registered")
	}

	// User management routes (protected)
	if app.userManagementHandler != nil {
		users := api.Group("/users", app.authMiddleware.RequireAuth())
		users.Post("/", app.userManagementHandler.CreateUser)
		users.Get("/", app.userManagementHandler.ListUsers)
		users.Get("/statistics", app.userManagementHandler.GetUserStatistics)
		users.Post("/bulk", app.userManagementHandler.BulkOperations)
		users.Get("/role/:role_id", app.userManagementHandler.GetUsersByRole)
		users.Get("/:id", app.userManagementHandler.GetUser)
		users.Put("/:id", app.userManagementHandler.UpdateUser)
		users.Delete("/:id", app.userManagementHandler.DeleteUser)
		users.Post("/:id/role", app.userManagementHandler.AssignRole)
		users.Delete("/:id/role", app.userManagementHandler.RemoveRole)
		users.Post("/:id/toggle-status", app.userManagementHandler.ToggleUserStatus)
		users.Put("/:id/password", app.userManagementHandler.UpdatePassword)
		users.Get("/:id/permissions", app.userManagementHandler.GetUserPermissions)
	} else {
		log.Println("WARNING: userManagementHandler is nil, routes not registered")
	}

	// Non-versioned API routes for frontend compatibility
	apiRoot := app.fiber.Group("/api")
	apiRoot.Use(middleware.APIRateLimit())

	// Pipeline routes (non-versioned, protected)
	if app.pipelineHandler != nil {
		pipelineRoutes := apiRoot.Group("/pipelines", app.authMiddleware.RequireAuth())
		pipelineRoutes.Get("/", app.pipelineHandler.ListPipelines)
		pipelineRoutes.Get("/default", app.pipelineHandler.GetDefaultPipeline)
		pipelineRoutes.Get("/:id", app.pipelineHandler.GetPipeline)
	}

	// Dashboard routes (non-versioned, protected)
	if app.dashboardHandler != nil {
		dashboardRoutes := apiRoot.Group("/dashboard", app.authMiddleware.RequireAuth())
		dashboardRoutes.Get("/stats", app.dashboardHandler.GetDashboardStats)
	}

	// Contact routes (non-versioned, protected)
	if app.contactHandler != nil {
		contactRoutes := apiRoot.Group("/contacts", app.authMiddleware.RequireAuth())
		contactRoutes.Post("/", app.contactHandler.CreateContact)
		contactRoutes.Get("/", app.contactHandler.ListContacts)
		contactRoutes.Get("/search", app.contactHandler.SearchContacts)
		contactRoutes.Get("/:id", app.contactHandler.GetContact)
		contactRoutes.Put("/:id", app.contactHandler.UpdateContact)
		contactRoutes.Delete("/:id", app.contactHandler.DeleteContact)
		contactRoutes.Patch("/:id/custom-fields", app.contactHandler.UpdateCustomFields)
	}

	// Company routes (non-versioned, protected)
	if app.companyHandler != nil {
		companyRoutes := apiRoot.Group("/companies", app.authMiddleware.RequireAuth())
		companyRoutes.Post("/", app.companyHandler.CreateCompany)
		companyRoutes.Get("/", app.companyHandler.ListCompanies)
		companyRoutes.Get("/search", app.companyHandler.SearchCompanies)
		companyRoutes.Get("/industry/:industry", app.companyHandler.GetCompaniesByIndustry)
		companyRoutes.Get("/:id", app.companyHandler.GetCompany)
		companyRoutes.Put("/:id", app.companyHandler.UpdateCompany)
		companyRoutes.Delete("/:id", app.companyHandler.DeleteCompany)
		companyRoutes.Patch("/:id/custom-fields", app.companyHandler.UpdateCustomFields)
		companyRoutes.Get("/:id/contacts", app.companyHandler.GetCompanyContacts)
	}
	
	// Serve static files (uploaded files)
	app.fiber.Static("/uploads", "./uploads")
}

// startBackgroundWorkers starts background services
func (app *App) startBackgroundWorkers(ctx context.Context) {
	if app.webhookService != nil {
		// Start webhook retry worker
		go app.webhookService.StartRetryWorker(ctx)
		
		// Start webhook cleanup worker
		go app.webhookService.StartCleanupWorker(ctx)
	}
	
	log.Println("✅ Background workers started")
}
server:
  host: "0.0.0.0"
  port: "8080"
  read_timeout: 60
  write_timeout: 60
  idle_timeout: 60

database:
  host: "localhost"
  port: "5432"
  user: "postgres"
  password: "postgres"
  dbname: "crm"
  sslmode: "disable"
  timezone: "UTC"

auth:
  jwt_secret: "your-super-secret-jwt-key-change-this-in-production"
  token_duration: 168 # 7 days in hours

storage:
  upload_path: "./uploads"
  max_size: 10485760 # 10MB in bytes

redis:
  host: "localhost"
  port: "6379"
  password: ""
  db: 0
package handlers

import (
	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/usecases"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

// AuthHandler handles authentication endpoints
type AuthHandler struct {
	authUsecase *usecases.AuthUsecase
	validator   *validator.Validate
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authUsecase *usecases.AuthUsecase) *AuthHandler {
	return &AuthHandler{
		authUsecase: authUsecase,
		validator:   validator.New(),
	}
}

// Login handles user login
// @Summary Login user
// @Description Authenticate user and return JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param loginRequest body usecases.LoginRequest true "Login credentials"
// @Success 200 {object} usecases.LoginResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /auth/login [post]
func (h *<PERSON>th<PERSON>and<PERSON>) Login(c *fiber.Ctx) error {
	var req usecases.LoginRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	response, err := h.authUsecase.Login(c.Context(), &req)
	if err != nil {
		switch err {
		case usecases.ErrInvalidCredentials:
			return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
				Error: "Invalid credentials",
			})
		case usecases.ErrUserInactive:
			return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
				Error: "Account is inactive",
			})
		default:
			return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
				Error: "Internal server error",
			})
		}
	}

	return c.JSON(SuccessResponse{
		Message: "Login successful",
		Data:    response,
	})
}

// Register handles user registration
// @Summary Register user
// @Description Create a new user account
// @Tags auth
// @Accept json
// @Produce json
// @Param registerRequest body usecases.RegisterRequest true "Registration data"
// @Success 201 {object} entities.User
// @Failure 400 {object} ErrorResponse
// @Router /auth/register [post]
func (h *AuthHandler) Register(c *fiber.Ctx) error {
	var req usecases.RegisterRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	user, err := h.authUsecase.Register(c.Context(), &req)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Message: "User registered successfully",
		Data:    user,
	})
}

// RefreshToken handles token refresh
// @Summary Refresh JWT token
// @Description Refresh an existing JWT token
// @Tags auth
// @Security BearerAuth
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 401 {object} ErrorResponse
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *fiber.Ctx) error {
	// Get current token from header
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Authorization header is required",
		})
	}

	tokenParts := splitAuthHeader(authHeader)
	if len(tokenParts) != 2 {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Invalid authorization header format",
		})
	}

	newToken, err := h.authUsecase.RefreshToken(c.Context(), tokenParts[1])
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Failed to refresh token",
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Token refreshed successfully",
		Data: map[string]string{
			"token": newToken,
		},
	})
}

// GetProfile handles getting user profile
// @Summary Get user profile
// @Description Get current user profile information
// @Tags auth
// @Security BearerAuth
// @Produce json
// @Success 200 {object} entities.User
// @Failure 401 {object} ErrorResponse
// @Router /auth/profile [get]
func (h *AuthHandler) GetProfile(c *fiber.Ctx) error {
	user := middleware.MustGetUserFromContext(c)
	return c.JSON(SuccessResponse{
		Message: "Profile retrieved successfully",
		Data:    user,
	})
}

// Logout handles user logout
// @Summary Logout user
// @Description Logout current user and blacklist token
// @Tags auth
// @Security BearerAuth
// @Produce json
// @Success 200 {object} map[string]string
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c *fiber.Ctx) error {
	// Extract token from Authorization header
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Authorization header required",
		})
	}
	
	// Extract token from "Bearer <token>" format
	const bearerPrefix = "Bearer "
	if len(authHeader) < len(bearerPrefix) {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid authorization format",
		})
	}
	
	token := authHeader[len(bearerPrefix):]
	
	// Blacklist the token
	if err := h.authUsecase.Logout(c.Context(), token); err != nil {
		// Log error but still return success - user intent is to logout
		// In production, you might want to handle this differently
		return c.JSON(SuccessResponse{
			Message: "Logged out successfully",
			Data:    map[string]string{"message": "Token invalidated"},
		})
	}
	
	return c.JSON(SuccessResponse{
		Message: "Logged out successfully",
		Data:    map[string]string{"message": "Token has been blacklisted"},
	})
}
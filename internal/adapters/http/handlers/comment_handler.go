package handlers

import (
	"crm-backend/internal/adapters/events"
	"crm-backend/internal/usecases"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

type CommentHandler struct {
	commentUseCase *usecases.CommentUseCase
	sseManager     *events.SSEManager
}

func NewCommentHandler(commentUseCase *usecases.CommentUseCase, sseManager *events.SSEManager) *CommentHandler {
	return &CommentHandler{
		commentUseCase: commentUseCase,
		sseManager:     sseManager,
	}
}

// CreateComment creates a new comment
func (h *CommentHandler) CreateComment(c *fiber.Ctx) error {
	userID := getUserIDFromContext(c)
	if userID == uuid.Nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "unauthorized",
		})
	}

	var input usecases.CreateCommentInput
	if err := c.BodyParser(&input); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid request body",
		})
	}

	comment, err := h.commentUseCase.CreateComment(c.Context(), userID, input)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Send SSE notification
	h.sseManager.SendToAll(events.Event{
		Type: "comment_created",
		Data: map[string]interface{}{
			"comment":     comment,
			"entity_type": input.EntityType,
			"entity_id":   input.EntityID,
		},
	})

	return c.Status(fiber.StatusCreated).JSON(comment)
}

// GetComment retrieves a single comment by ID
func (h *CommentHandler) GetComment(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid comment ID",
		})
	}

	comment, err := h.commentUseCase.GetComment(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "comment not found",
		})
	}

	return c.JSON(comment)
}

// GetCardComments retrieves all comments for a card
func (h *CommentHandler) GetCardComments(c *fiber.Ctx) error {
	cardID, err := uuid.Parse(c.Params("cardId"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid card ID",
		})
	}

	comments, err := h.commentUseCase.GetCardComments(c.Context(), cardID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(comments)
}

// GetEntityComments retrieves all comments for an entity
func (h *CommentHandler) GetEntityComments(c *fiber.Ctx) error {
	entityType := c.Params("entityType")
	entityID, err := uuid.Parse(c.Params("entityId"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid entity ID",
		})
	}

	comments, err := h.commentUseCase.GetEntityComments(c.Context(), entityType, entityID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(comments)
}

// UpdateComment updates an existing comment
func (h *CommentHandler) UpdateComment(c *fiber.Ctx) error {
	userID := getUserIDFromContext(c)
	if userID == uuid.Nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "unauthorized",
		})
	}

	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid comment ID",
		})
	}

	var input usecases.UpdateCommentInput
	if err := c.BodyParser(&input); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid request body",
		})
	}

	comment, err := h.commentUseCase.UpdateComment(c.Context(), id, userID, input)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Send SSE notification
	h.sseManager.SendToAll(events.Event{
		Type: "comment_updated",
		Data: map[string]interface{}{
			"comment": comment,
		},
	})

	return c.JSON(comment)
}

// DeleteComment deletes a comment
func (h *CommentHandler) DeleteComment(c *fiber.Ctx) error {
	userID := getUserIDFromContext(c)
	if userID == uuid.Nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "unauthorized",
		})
	}

	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid comment ID",
		})
	}

	if err := h.commentUseCase.DeleteComment(c.Context(), id, userID); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Send SSE notification
	h.sseManager.SendToAll(events.Event{
		Type: "comment_deleted",
		Data: map[string]interface{}{
			"comment_id": id,
		},
	})

	return c.JSON(fiber.Map{
		"message": "comment deleted successfully",
	})
}

// PinComment pins a comment
func (h *CommentHandler) PinComment(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid comment ID",
		})
	}

	if err := h.commentUseCase.PinComment(c.Context(), id); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Send SSE notification
	h.sseManager.SendToAll(events.Event{
		Type: "comment_pinned",
		Data: map[string]interface{}{
			"comment_id": id,
		},
	})

	return c.JSON(fiber.Map{
		"message": "comment pinned successfully",
	})
}

// UnpinComment unpins a comment
func (h *CommentHandler) UnpinComment(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid comment ID",
		})
	}

	if err := h.commentUseCase.UnpinComment(c.Context(), id); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Send SSE notification
	h.sseManager.SendToAll(events.Event{
		Type: "comment_unpinned",
		Data: map[string]interface{}{
			"comment_id": id,
		},
	})

	return c.JSON(fiber.Map{
		"message": "comment unpinned successfully",
	})
}

// AddReaction adds a reaction to a comment
func (h *CommentHandler) AddReaction(c *fiber.Ctx) error {
	userID := getUserIDFromContext(c)
	if userID == uuid.Nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "unauthorized",
		})
	}

	commentID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid comment ID",
		})
	}

	var input struct {
		Reaction string `json:"reaction" validate:"required"`
	}
	if err := c.BodyParser(&input); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid request body",
		})
	}

	if err := h.commentUseCase.AddReaction(c.Context(), commentID, userID, input.Reaction); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Send SSE notification
	h.sseManager.SendToAll(events.Event{
		Type: "reaction_added",
		Data: map[string]interface{}{
			"comment_id": commentID,
			"user_id":    userID,
			"reaction":   input.Reaction,
		},
	})

	return c.JSON(fiber.Map{
		"message": "reaction added successfully",
	})
}

// RemoveReaction removes a reaction from a comment
func (h *CommentHandler) RemoveReaction(c *fiber.Ctx) error {
	userID := getUserIDFromContext(c)
	if userID == uuid.Nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "unauthorized",
		})
	}

	commentID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid comment ID",
		})
	}

	reaction := c.Params("reaction")
	if reaction == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "reaction is required",
		})
	}

	if err := h.commentUseCase.RemoveReaction(c.Context(), commentID, userID, reaction); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Send SSE notification
	h.sseManager.SendToAll(events.Event{
		Type: "reaction_removed",
		Data: map[string]interface{}{
			"comment_id": commentID,
			"user_id":    userID,
			"reaction":   reaction,
		},
	})

	return c.JSON(fiber.Map{
		"message": "reaction removed successfully",
	})
}

// GetCommentEditHistory retrieves edit history for a comment
func (h *CommentHandler) GetCommentEditHistory(c *fiber.Ctx) error {
	commentID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid comment ID",
		})
	}

	history, err := h.commentUseCase.GetCommentEditHistory(c.Context(), commentID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(history)
}

// RegisterRoutes registers all comment routes
func (h *CommentHandler) RegisterRoutes(app fiber.Router) {
	comments := app.Group("/comments")

	// Basic CRUD
	comments.Post("/", h.CreateComment)
	comments.Get("/:id", h.GetComment)
	comments.Put("/:id", h.UpdateComment)
	comments.Delete("/:id", h.DeleteComment)

	// Pin/Unpin
	comments.Post("/:id/pin", h.PinComment)
	comments.Delete("/:id/pin", h.UnpinComment)

	// Reactions
	comments.Post("/:id/reactions", h.AddReaction)
	comments.Delete("/:id/reactions/:reaction", h.RemoveReaction)

	// Edit history
	comments.Get("/:id/history", h.GetCommentEditHistory)

	// Entity-specific routes
	app.Get("/cards/:cardId/comments", h.GetCardComments)
	app.Get("/:entityType/:entityId/comments", h.GetEntityComments)
}

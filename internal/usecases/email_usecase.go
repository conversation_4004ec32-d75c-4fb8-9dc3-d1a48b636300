package usecases

import (
	"context"
	"fmt"
	"strings"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"crm-backend/internal/infrastructure/email"

	"github.com/google/uuid"
)

type EmailUseCase struct {
	emailRepo    repositories.EmailRepository
	cardRepo     repositories.CardRepository
	contactRepo  repositories.ContactRepository
	companyRepo  repositories.CompanyRepository
	userRepo     repositories.UserRepository
	activityRepo repositories.ActivityRepository
	smtpService  *email.SMTPService
}

func NewEmailUseCase(
	emailRepo repositories.EmailRepository,
	cardRepo repositories.CardRepository,
	contactRepo repositories.ContactRepository,
	companyRepo repositories.CompanyRepository,
	userRepo repositories.UserRepository,
	activityRepo repositories.ActivityRepository,
	smtpService *email.SMTPService,
) *EmailUseCase {
	return &EmailUseCase{
		emailRepo:    emailRepo,
		cardRepo:     cardRepo,
		contactRepo:  contactRepo,
		companyRepo:  companyRepo,
		userRepo:     userRepo,
		activityRepo: activityRepo,
		smtpService:  smtpService,
	}
}

type SendEmailInput struct {
	To         []string               `json:"to" validate:"required,dive,email"`
	CC         []string               `json:"cc,omitempty" validate:"omitempty,dive,email"`
	BCC        []string               `json:"bcc,omitempty" validate:"omitempty,dive,email"`
	Subject    string                 `json:"subject" validate:"required"`
	Body       string                 `json:"body" validate:"required"`
	BodyHTML   string                 `json:"body_html,omitempty"`
	ReplyTo    string                 `json:"reply_to,omitempty" validate:"omitempty,email"`
	CardID     *uuid.UUID             `json:"card_id,omitempty"`
	ContactID  *uuid.UUID             `json:"contact_id,omitempty"`
	CompanyID  *uuid.UUID             `json:"company_id,omitempty"`
	TemplateID *uuid.UUID             `json:"template_id,omitempty"`
	Variables  map[string]interface{} `json:"variables,omitempty"`
}

// SendEmail sends an email and records it in the database
func (uc *EmailUseCase) SendEmail(ctx context.Context, userID uuid.UUID, input SendEmailInput) (*entities.Email, error) {
	// Create email record
	emailRecord := &entities.Email{
		Subject:   input.Subject,
		Body:      input.Body,
		BodyHTML:  input.BodyHTML,
		From:      uc.smtpService.GetFromAddress(),
		To:        input.To,
		CC:        input.CC,
		BCC:       input.BCC,
		ReplyTo:   input.ReplyTo,
		Status:    entities.EmailStatusDraft,
		Type:      entities.EmailTypeManual,
		CardID:    input.CardID,
		ContactID: input.ContactID,
		CompanyID: input.CompanyID,
		UserID:    &userID,
		MessageID: uuid.New().String(),
		Metadata:  make(entities.CustomFields),
	}

	// If template is provided, load and apply it
	if input.TemplateID != nil {
		template, err := uc.emailRepo.GetTemplateByID(ctx, *input.TemplateID)
		if err != nil {
			return nil, fmt.Errorf("failed to load template: %w", err)
		}

		// Apply template variables
		if template.Body != "" && input.Variables != nil {
			emailRecord.Body = uc.applyVariables(template.Body, input.Variables)
		}
		if template.BodyHTML != "" && input.Variables != nil {
			emailRecord.BodyHTML = uc.applyVariables(template.BodyHTML, input.Variables)
		}
		if template.Subject != "" && input.Variables == nil {
			emailRecord.Subject = template.Subject
		}

		emailRecord.TemplateID = input.TemplateID
	}

	// Save email record
	if err := uc.emailRepo.Create(ctx, emailRecord); err != nil {
		return nil, fmt.Errorf("failed to create email record: %w", err)
	}

	// Send email via SMTP
	msg := &email.EmailMessage{
		To:      input.To,
		CC:      input.CC,
		BCC:     input.BCC,
		Subject: input.Subject,
		Body:    emailRecord.Body,
		HTML:    emailRecord.BodyHTML,
		ReplyTo: input.ReplyTo,
	}

	if err := uc.smtpService.SendEmail(msg); err != nil {
		// Mark as failed
		emailRecord.MarkAsFailed()
		_ = uc.emailRepo.Update(ctx, emailRecord)
		return nil, fmt.Errorf("failed to send email: %w", err)
	}

	// Mark as sent
	emailRecord.MarkAsSent()
	if err := uc.emailRepo.Update(ctx, emailRecord); err != nil {
		return nil, fmt.Errorf("failed to update email status: %w", err)
	}

	// Create activity record
	uc.createEmailActivity(ctx, userID, emailRecord)

	return emailRecord, nil
}

// GetEmail retrieves an email by ID
func (uc *EmailUseCase) GetEmail(ctx context.Context, id uuid.UUID) (*entities.Email, error) {
	return uc.emailRepo.GetByID(ctx, id)
}

// ListEmails lists emails with filters
func (uc *EmailUseCase) ListEmails(ctx context.Context, filters EmailFilters, limit, offset int) ([]*entities.Email, int64, error) {
	return uc.emailRepo.List(ctx, filters.ToRepositoryFilters(), limit, offset)
}

// ListEmailsByCard lists emails for a specific card
func (uc *EmailUseCase) ListEmailsByCard(ctx context.Context, cardID uuid.UUID) ([]*entities.Email, error) {
	return uc.emailRepo.GetByCardID(ctx, cardID)
}

// ListEmailsByContact lists emails for a specific contact
func (uc *EmailUseCase) ListEmailsByContact(ctx context.Context, contactID uuid.UUID) ([]*entities.Email, error) {
	return uc.emailRepo.GetByContactID(ctx, contactID)
}

// ListEmailsByCompany lists emails for a specific company
func (uc *EmailUseCase) ListEmailsByCompany(ctx context.Context, companyID uuid.UUID) ([]*entities.Email, error) {
	return uc.emailRepo.GetByCompanyID(ctx, companyID)
}

// TrackEmailOpen tracks when an email is opened
func (uc *EmailUseCase) TrackEmailOpen(ctx context.Context, emailID uuid.UUID) error {
	emailRecord, err := uc.emailRepo.GetByID(ctx, emailID)
	if err != nil {
		return err
	}

	emailRecord.MarkAsOpened()
	return uc.emailRepo.Update(ctx, emailRecord)
}

// TrackEmailClick tracks when a link in an email is clicked
func (uc *EmailUseCase) TrackEmailClick(ctx context.Context, emailID uuid.UUID) error {
	emailRecord, err := uc.emailRepo.GetByID(ctx, emailID)
	if err != nil {
		return err
	}

	emailRecord.MarkAsClicked()
	return uc.emailRepo.Update(ctx, emailRecord)
}

// CreateEmailTemplate creates a new email template
func (uc *EmailUseCase) CreateEmailTemplate(ctx context.Context, userID uuid.UUID, template *entities.EmailTemplate) error {
	template.CreatedByID = &userID
	return uc.emailRepo.CreateTemplate(ctx, template)
}

// GetEmailTemplate retrieves an email template by ID
func (uc *EmailUseCase) GetEmailTemplate(ctx context.Context, id uuid.UUID) (*entities.EmailTemplate, error) {
	return uc.emailRepo.GetTemplateByID(ctx, id)
}

// ListEmailTemplates lists all email templates
func (uc *EmailUseCase) ListEmailTemplates(ctx context.Context) ([]*entities.EmailTemplate, error) {
	return uc.emailRepo.ListTemplates(ctx)
}

// UpdateEmailTemplate updates an email template
func (uc *EmailUseCase) UpdateEmailTemplate(ctx context.Context, template *entities.EmailTemplate) error {
	return uc.emailRepo.UpdateTemplate(ctx, template)
}

// DeleteEmailTemplate deletes an email template
func (uc *EmailUseCase) DeleteEmailTemplate(ctx context.Context, id uuid.UUID) error {
	return uc.emailRepo.DeleteTemplate(ctx, id)
}

// TestSMTPConnection tests the SMTP connection
func (uc *EmailUseCase) TestSMTPConnection() error {
	return uc.smtpService.TestConnection()
}

// Helper functions

func (uc *EmailUseCase) createEmailActivity(ctx context.Context, userID uuid.UUID, emailRecord *entities.Email) {
	activity := &entities.Activity{
		Type:        entities.ActivityTypeEmail,
		Title:       fmt.Sprintf("Email sent: %s", emailRecord.Subject),
		Description: fmt.Sprintf("Sent email to %v", emailRecord.To),
		UserID:      &userID,
		CardID:      emailRecord.CardID,
		ContactID:   emailRecord.ContactID,
		CompanyID:   emailRecord.CompanyID,
		Metadata: entities.CustomFields{
			"email_id": emailRecord.ID.String(),
			"subject":  emailRecord.Subject,
			"to":       emailRecord.To,
		},
	}

	_ = uc.activityRepo.Create(ctx, activity)
}

func (uc *EmailUseCase) applyVariables(template string, variables map[string]interface{}) string {
	result := template
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, placeholder, fmt.Sprintf("%v", value))
	}
	return result
}

// EmailFilters represents filters for listing emails
type EmailFilters struct {
	Status    *entities.EmailStatus
	Type      *entities.EmailType
	CardID    *uuid.UUID
	ContactID *uuid.UUID
	CompanyID *uuid.UUID
	UserID    *uuid.UUID
	From      *time.Time
	To        *time.Time
}

func (f *EmailFilters) ToRepositoryFilters() map[string]interface{} {
	filters := make(map[string]interface{})

	if f.Status != nil {
		filters["status"] = *f.Status
	}
	if f.Type != nil {
		filters["type"] = *f.Type
	}
	if f.CardID != nil {
		filters["card_id"] = *f.CardID
	}
	if f.ContactID != nil {
		filters["contact_id"] = *f.ContactID
	}
	if f.CompanyID != nil {
		filters["company_id"] = *f.CompanyID
	}
	if f.UserID != nil {
		filters["user_id"] = *f.UserID
	}
	if f.From != nil {
		filters["from_date"] = *f.From
	}
	if f.To != nil {
		filters["to_date"] = *f.To
	}

	return filters
}

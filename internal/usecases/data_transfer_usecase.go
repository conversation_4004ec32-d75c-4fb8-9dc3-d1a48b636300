package usecases

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"

	"github.com/google/uuid"
	"github.com/xuri/excelize/v2"
)

type DataTransferUseCase struct {
	transferRepo repositories.DataTransferRepository
	cardRepo     repositories.CardRepository
	contactRepo  repositories.ContactRepository
	companyRepo  repositories.CompanyRepository
	userRepo     repositories.UserRepository
	uploadPath   string
}

func NewDataTransferUseCase(
	transferRepo repositories.DataTransferRepository,
	cardRepo repositories.CardRepository,
	contactRepo repositories.ContactRepository,
	companyRepo repositories.CompanyRepository,
	userRepo repositories.UserRepository,
	uploadPath string,
) *DataTransferUseCase {
	return &DataTransferUseCase{
		transferRepo: transferRepo,
		cardRepo:     cardRepo,
		contactRepo:  contactRepo,
		companyRepo:  companyRepo,
		userRepo:     userRepo,
		uploadPath:   uploadPath,
	}
}

// StartImport initiates an import operation
func (uc *DataTransferUseCase) StartImport(ctx context.Context, userID uuid.UUID, entityType string, fileName string, filePath string, format entities.DataTransferFormat) (*entities.DataTransfer, error) {
	transfer := &entities.DataTransfer{
		Type:        entities.DataTransferTypeImport,
		EntityType:  entityType,
		FileName:    fileName,
		FilePath:    filePath,
		FileFormat:  format,
		Status:      entities.DataTransferStatusPending,
		CreatedByID: userID,
		Mapping:     make(entities.CustomFields),
		ErrorLog:    make(entities.CustomFields),
	}

	if err := uc.transferRepo.Create(ctx, transfer); err != nil {
		return nil, err
	}

	// Start import in background
	go uc.processImport(context.Background(), transfer.ID)

	return transfer, nil
}

// StartExport initiates an export operation
func (uc *DataTransferUseCase) StartExport(ctx context.Context, userID uuid.UUID, entityType string, format entities.DataTransferFormat, filters entities.CustomFields) (*entities.DataTransfer, error) {
	fileName := fmt.Sprintf("%s_export_%s.%s", entityType, time.Now().Format("20060102_150405"), string(format))
	filePath := filepath.Join(uc.uploadPath, "exports", fileName)

	transfer := &entities.DataTransfer{
		Type:        entities.DataTransferTypeExport,
		EntityType:  entityType,
		FileName:    fileName,
		FilePath:    filePath,
		FileFormat:  format,
		Status:      entities.DataTransferStatusPending,
		CreatedByID: userID,
		Filters:     filters,
		ErrorLog:    make(entities.CustomFields),
	}

	if err := uc.transferRepo.Create(ctx, transfer); err != nil {
		return nil, err
	}

	// Start export in background
	go uc.processExport(context.Background(), transfer.ID)

	return transfer, nil
}

// GetTransferStatus gets the status of a transfer
func (uc *DataTransferUseCase) GetTransferStatus(ctx context.Context, id uuid.UUID) (*entities.DataTransfer, error) {
	return uc.transferRepo.GetByID(ctx, id)
}

// ListTransfers lists transfers for a user
func (uc *DataTransferUseCase) ListTransfers(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*entities.DataTransfer, int64, error) {
	return uc.transferRepo.List(ctx, userID, limit, offset)
}

// processImport processes an import operation
func (uc *DataTransferUseCase) processImport(ctx context.Context, transferID uuid.UUID) {
	transfer, err := uc.transferRepo.GetByID(ctx, transferID)
	if err != nil {
		return
	}

	transfer.Start()
	_ = uc.transferRepo.Update(ctx, transfer)

	defer func() {
		if r := recover(); r != nil {
			transfer.Fail(fmt.Sprintf("Import failed: %v", r))
			_ = uc.transferRepo.Update(ctx, transfer)
		}
	}()

	// Open file based on format
	var records [][]string
	switch transfer.FileFormat {
	case entities.DataTransferFormatCSV:
		records, err = uc.readCSV(transfer.FilePath)
	case entities.DataTransferFormatXLSX:
		records, err = uc.readExcel(transfer.FilePath)
	default:
		err = fmt.Errorf("unsupported format: %s", transfer.FileFormat)
	}

	if err != nil {
		transfer.Fail(err.Error())
		_ = uc.transferRepo.Update(ctx, transfer)
		return
	}

	if len(records) < 2 {
		transfer.Fail("No data to import")
		_ = uc.transferRepo.Update(ctx, transfer)
		return
	}

	headers := records[0]
	transfer.TotalRecords = len(records) - 1

	// Process records based on entity type
	switch transfer.EntityType {
	case "cards":
		uc.importCards(ctx, transfer, headers, records[1:])
	case "contacts":
		uc.importContacts(ctx, transfer, headers, records[1:])
	case "companies":
		uc.importCompanies(ctx, transfer, headers, records[1:])
	default:
		transfer.Fail(fmt.Sprintf("Unsupported entity type: %s", transfer.EntityType))
	}

	transfer.Complete()
	_ = uc.transferRepo.Update(ctx, transfer)
}

// processExport processes an export operation
func (uc *DataTransferUseCase) processExport(ctx context.Context, transferID uuid.UUID) {
	transfer, err := uc.transferRepo.GetByID(ctx, transferID)
	if err != nil {
		return
	}

	transfer.Start()
	_ = uc.transferRepo.Update(ctx, transfer)

	defer func() {
		if r := recover(); r != nil {
			transfer.Fail(fmt.Sprintf("Export failed: %v", r))
			_ = uc.transferRepo.Update(ctx, transfer)
		}
	}()

	// Ensure export directory exists
	exportDir := filepath.Dir(transfer.FilePath)
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		transfer.Fail(fmt.Sprintf("Failed to create export directory: %v", err))
		_ = uc.transferRepo.Update(ctx, transfer)
		return
	}

	// Export data based on entity type
	var data [][]string
	switch transfer.EntityType {
	case "cards":
		data, err = uc.exportCards(ctx, transfer.Filters)
	case "contacts":
		data, err = uc.exportContacts(ctx, transfer.Filters)
	case "companies":
		data, err = uc.exportCompanies(ctx, transfer.Filters)
	default:
		err = fmt.Errorf("unsupported entity type: %s", transfer.EntityType)
	}

	if err != nil {
		transfer.Fail(err.Error())
		_ = uc.transferRepo.Update(ctx, transfer)
		return
	}

	transfer.TotalRecords = len(data) - 1 // Exclude header
	transfer.ProcessedRecords = transfer.TotalRecords
	transfer.SuccessRecords = transfer.TotalRecords

	// Write to file based on format
	switch transfer.FileFormat {
	case entities.DataTransferFormatCSV:
		err = uc.WriteCSV(transfer.FilePath, data)
	case entities.DataTransferFormatXLSX:
		err = uc.WriteExcel(transfer.FilePath, data, transfer.EntityType)
	default:
		err = fmt.Errorf("unsupported format: %s", transfer.FileFormat)
	}

	if err != nil {
		transfer.Fail(err.Error())
		_ = uc.transferRepo.Update(ctx, transfer)
		return
	}

	transfer.Complete()
	_ = uc.transferRepo.Update(ctx, transfer)
}

// importCards imports card records
func (uc *DataTransferUseCase) importCards(ctx context.Context, transfer *entities.DataTransfer, headers []string, records [][]string) {
	// Map headers to field indices
	fieldMap := make(map[string]int)
	for i, header := range headers {
		fieldMap[strings.ToLower(header)] = i
	}

	for rowNum, record := range records {
		transfer.ProcessedRecords++

		card := &entities.Card{
			CustomFields: make(entities.CustomFields),
		}

		// Map standard fields
		if idx, ok := fieldMap["title"]; ok && idx < len(record) {
			card.Title = record[idx]
		}
		if idx, ok := fieldMap["description"]; ok && idx < len(record) {
			card.Description = record[idx]
		}
		if idx, ok := fieldMap["value"]; ok && idx < len(record) {
			if val, err := strconv.ParseFloat(record[idx], 64); err == nil {
				card.Value = val
			}
		}
		if idx, ok := fieldMap["priority"]; ok && idx < len(record) {
			card.Priority = record[idx]
		}
		if idx, ok := fieldMap["due_date"]; ok && idx < len(record) && record[idx] != "" {
			if t, err := time.Parse("2006-01-02", record[idx]); err == nil {
				card.DueDate = &t
			}
		}

		// Map custom fields
		for i, value := range record {
			if i < len(headers) {
				header := headers[i]
				// Skip standard fields
				if !isStandardCardField(header) && value != "" {
					card.CustomFields[header] = value
				}
			}
		}

		// Create card
		if err := uc.cardRepo.Create(ctx, card); err != nil {
			transfer.AddError(rowNum+2, "card", err.Error())
		} else {
			transfer.SuccessRecords++
		}

		transfer.UpdateProgress()
		_ = uc.transferRepo.Update(ctx, transfer)
	}
}

// importContacts imports contact records
func (uc *DataTransferUseCase) importContacts(ctx context.Context, transfer *entities.DataTransfer, headers []string, records [][]string) {
	fieldMap := make(map[string]int)
	for i, header := range headers {
		fieldMap[strings.ToLower(header)] = i
	}

	for rowNum, record := range records {
		transfer.ProcessedRecords++

		contact := &entities.Contact{
			CustomFields: make(entities.CustomFields),
		}

		// Map standard fields
		if idx, ok := fieldMap["first_name"]; ok && idx < len(record) {
			contact.FirstName = record[idx]
		}
		if idx, ok := fieldMap["last_name"]; ok && idx < len(record) {
			contact.LastName = record[idx]
		}
		if idx, ok := fieldMap["email"]; ok && idx < len(record) {
			contact.Email = record[idx]
		}
		if idx, ok := fieldMap["phone"]; ok && idx < len(record) {
			contact.Phone = record[idx]
		}
		if idx, ok := fieldMap["position"]; ok && idx < len(record) {
			contact.Position = record[idx]
		}

		// Map custom fields
		for i, value := range record {
			if i < len(headers) {
				header := headers[i]
				if !isStandardContactField(header) && value != "" {
					contact.CustomFields[header] = value
				}
			}
		}

		// Create contact
		if err := uc.contactRepo.Create(ctx, contact); err != nil {
			transfer.AddError(rowNum+2, "contact", err.Error())
		} else {
			transfer.SuccessRecords++
		}

		transfer.UpdateProgress()
		_ = uc.transferRepo.Update(ctx, transfer)
	}
}

// importCompanies imports company records
func (uc *DataTransferUseCase) importCompanies(ctx context.Context, transfer *entities.DataTransfer, headers []string, records [][]string) {
	fieldMap := make(map[string]int)
	for i, header := range headers {
		fieldMap[strings.ToLower(header)] = i
	}

	for rowNum, record := range records {
		transfer.ProcessedRecords++

		company := &entities.Company{
			CustomFields: make(entities.CustomFields),
		}

		// Map standard fields
		if idx, ok := fieldMap["name"]; ok && idx < len(record) {
			company.Name = record[idx]
		}
		if idx, ok := fieldMap["domain"]; ok && idx < len(record) {
			company.Domain = record[idx]
		}
		if idx, ok := fieldMap["industry"]; ok && idx < len(record) {
			company.Industry = record[idx]
		}
		if idx, ok := fieldMap["size"]; ok && idx < len(record) {
			company.Size = record[idx]
		}
		if idx, ok := fieldMap["annual_revenue"]; ok && idx < len(record) {
			if val, err := strconv.ParseFloat(record[idx], 64); err == nil {
				company.AnnualRevenue = val
			}
		}
		if idx, ok := fieldMap["website"]; ok && idx < len(record) {
			company.Website = record[idx]
		}

		// Map custom fields
		for i, value := range record {
			if i < len(headers) {
				header := headers[i]
				if !isStandardCompanyField(header) && value != "" {
					company.CustomFields[header] = value
				}
			}
		}

		// Create company
		if err := uc.companyRepo.Create(ctx, company); err != nil {
			transfer.AddError(rowNum+2, "company", err.Error())
		} else {
			transfer.SuccessRecords++
		}

		transfer.UpdateProgress()
		_ = uc.transferRepo.Update(ctx, transfer)
	}
}

// exportCards exports card data
func (uc *DataTransferUseCase) exportCards(ctx context.Context, filters entities.CustomFields) ([][]string, error) {
	cards, err := uc.cardRepo.List(ctx, 10000, 0)
	if err != nil {
		return nil, err
	}

	data := [][]string{
		{"ID", "Title", "Description", "Value", "Priority", "Status", "Due Date", "Created At"},
	}

	for _, card := range cards {
		row := []string{
			card.ID.String(),
			card.Title,
			card.Description,
			fmt.Sprintf("%.2f", card.Value),
			card.Priority,
			card.Status,
			"",
			card.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		if card.DueDate != nil {
			row[6] = card.DueDate.Format("2006-01-02")
		}

		data = append(data, row)
	}

	return data, nil
}

// exportContacts exports contact data
func (uc *DataTransferUseCase) exportContacts(ctx context.Context, filters entities.CustomFields) ([][]string, error) {
	contacts, err := uc.contactRepo.List(ctx, 10000, 0)
	if err != nil {
		return nil, err
	}

	data := [][]string{
		{"ID", "First Name", "Last Name", "Email", "Phone", "Position", "Created At"},
	}

	for _, contact := range contacts {
		row := []string{
			contact.ID.String(),
			contact.FirstName,
			contact.LastName,
			contact.Email,
			contact.Phone,
			contact.Position,
			contact.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		data = append(data, row)
	}

	return data, nil
}

// exportCompanies exports company data
func (uc *DataTransferUseCase) exportCompanies(ctx context.Context, filters entities.CustomFields) ([][]string, error) {
	companies, err := uc.companyRepo.List(ctx, 10000, 0)
	if err != nil {
		return nil, err
	}

	data := [][]string{
		{"ID", "Name", "Domain", "Industry", "Size", "Annual Revenue", "Website", "Created At"},
	}

	for _, company := range companies {
		row := []string{
			company.ID.String(),
			company.Name,
			company.Domain,
			company.Industry,
			company.Size,
			fmt.Sprintf("%.2f", company.AnnualRevenue),
			company.Website,
			company.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		data = append(data, row)
	}

	return data, nil
}

// readCSV reads a CSV file
func (uc *DataTransferUseCase) readCSV(filePath string) ([][]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	return reader.ReadAll()
}

// WriteCSV writes data to a CSV file
func (uc *DataTransferUseCase) WriteCSV(filePath string, data [][]string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	for _, row := range data {
		if err := writer.Write(row); err != nil {
			return err
		}
	}

	return nil
}

// readExcel reads an Excel file
func (uc *DataTransferUseCase) readExcel(filePath string) ([][]string, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	// Get first sheet
	sheets := f.GetSheetList()
	if len(sheets) == 0 {
		return nil, fmt.Errorf("no sheets found in Excel file")
	}

	rows, err := f.GetRows(sheets[0])
	if err != nil {
		return nil, err
	}

	return rows, nil
}

// WriteExcel writes data to an Excel file
func (uc *DataTransferUseCase) WriteExcel(filePath string, data [][]string, sheetName string) error {
	f := excelize.NewFile()

	// Create sheet
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return err
	}
	f.SetActiveSheet(index)

	// Write data
	for i, row := range data {
		for j, cell := range row {
			cellName, _ := excelize.CoordinatesToCellName(j+1, i+1)
			f.SetCellValue(sheetName, cellName, cell)
		}
	}

	// Style header row
	if len(data) > 0 {
		style, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{Bold: true},
			Fill: excelize.Fill{Type: "pattern", Color: []string{"#E0E0E0"}, Pattern: 1},
		})
		f.SetRowStyle(sheetName, 1, 1, style)
	}

	// Auto-fit columns
	cols, _ := f.GetCols(sheetName)
	for idx := range cols {
		col, _ := excelize.ColumnNumberToName(idx + 1)
		f.SetColWidth(sheetName, col, col, 15)
	}

	return f.SaveAs(filePath)
}

// Helper functions
func isStandardCardField(field string) bool {
	standardFields := []string{"title", "description", "value", "priority", "status", "due_date", "stage_id", "pipeline_id"}
	field = strings.ToLower(field)
	for _, sf := range standardFields {
		if field == sf {
			return true
		}
	}
	return false
}

func isStandardContactField(field string) bool {
	standardFields := []string{"first_name", "last_name", "email", "phone", "position", "company_id"}
	field = strings.ToLower(field)
	for _, sf := range standardFields {
		if field == sf {
			return true
		}
	}
	return false
}

func isStandardCompanyField(field string) bool {
	standardFields := []string{"name", "domain", "industry", "size", "annual_revenue", "website", "description"}
	field = strings.ToLower(field)
	for _, sf := range standardFields {
		if field == sf {
			return true
		}
	}
	return false
}

// GetExportFile returns the file path for download
func (uc *DataTransferUseCase) GetExportFile(ctx context.Context, transferID uuid.UUID) (string, string, error) {
	transfer, err := uc.transferRepo.GetByID(ctx, transferID)
	if err != nil {
		return "", "", err
	}

	if transfer.Type != entities.DataTransferTypeExport {
		return "", "", fmt.Errorf("not an export transfer")
	}

	if transfer.Status != entities.DataTransferStatusCompleted {
		return "", "", fmt.Errorf("export not completed")
	}

	return transfer.FilePath, transfer.FileName, nil
}

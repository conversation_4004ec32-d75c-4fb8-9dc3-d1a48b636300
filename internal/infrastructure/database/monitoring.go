package database

import (
	"context"
	"database/sql"
	"log"
	"time"

	"gorm.io/gorm"
)

// ConnectionPoolMonitor monitors database connection pool health
type ConnectionPoolMonitor struct {
	db       *gorm.DB
	sqlDB    *sql.DB
	interval time.Duration
	ctx      context.Context
	cancel   context.CancelFunc
}

// NewConnectionPoolMonitor creates a new connection pool monitor
func NewConnectionPoolMonitor(db *gorm.DB, interval time.Duration) (*ConnectionPoolMonitor, error) {
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &ConnectionPoolMonitor{
		db:       db,
		sqlDB:    sqlDB,
		interval: interval,
		ctx:      ctx,
		cancel:   cancel,
	}, nil
}

// Start begins monitoring the connection pool
func (m *ConnectionPoolMonitor) Start() {
	go m.monitor()
}

// Stop stops monitoring
func (m *ConnectionPoolMonitor) Stop() {
	m.cancel()
}

// monitor runs the monitoring loop
func (m *ConnectionPoolMonitor) monitor() {
	ticker := time.NewTicker(m.interval)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.logStats()
		}
	}
}

// logStats logs the current connection pool statistics
func (m *ConnectionPoolMonitor) logStats() {
	stats := m.sqlDB.Stats()

	// Log if we're getting close to limits
	utilizationPercent := float64(stats.OpenConnections) / float64(stats.MaxOpenConnections) * 100

	if utilizationPercent > 80 {
		log.Printf("⚠️  WARNING: Database connection pool utilization high: %.1f%% (%d/%d connections)",
			utilizationPercent, stats.OpenConnections, stats.MaxOpenConnections)
	} else if utilizationPercent > 60 {
		log.Printf("📊 Database pool: %.1f%% utilized (%d/%d connections, %d idle, %d in use)",
			utilizationPercent, stats.OpenConnections, stats.MaxOpenConnections,
			stats.Idle, stats.InUse)
	}

	// Log wait statistics if significant
	if stats.WaitCount > 0 {
		avgWait := stats.WaitDuration / time.Duration(stats.WaitCount)
		if avgWait > 100*time.Millisecond {
			log.Printf("⚠️  Database connection wait time high: avg %.2fms over %d waits",
				float64(avgWait)/float64(time.Millisecond), stats.WaitCount)
		}
	}
}

// GetStats returns current connection pool statistics
func (m *ConnectionPoolMonitor) GetStats() sql.DBStats {
	return m.sqlDB.Stats()
}

// HealthCheck performs a health check on the database connection
func (m *ConnectionPoolMonitor) HealthCheck() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Ping the database
	if err := m.sqlDB.PingContext(ctx); err != nil {
		return err
	}

	// Check connection pool health
	stats := m.sqlDB.Stats()
	if stats.OpenConnections == stats.MaxOpenConnections && stats.WaitCount > 10 {
		log.Printf("⚠️  Database connection pool exhausted: %d/%d connections, %d waiting",
			stats.OpenConnections, stats.MaxOpenConnections, stats.WaitCount)
	}

	return nil
}

// OptimizePool adjusts pool settings based on usage patterns
func (m *ConnectionPoolMonitor) OptimizePool() {
	stats := m.sqlDB.Stats()

	// If we're consistently using most connections, consider increasing the pool
	utilizationPercent := float64(stats.OpenConnections) / float64(stats.MaxOpenConnections) * 100

	if utilizationPercent > 90 && stats.MaxOpenConnections < 75 {
		newMax := stats.MaxOpenConnections + 10
		m.sqlDB.SetMaxOpenConns(newMax)
		log.Printf("🔧 Increased max database connections to %d due to high utilization", newMax)
	} else if utilizationPercent < 30 && stats.MaxOpenConnections > 30 {
		newMax := stats.MaxOpenConnections - 10
		m.sqlDB.SetMaxOpenConns(newMax)
		log.Printf("🔧 Decreased max database connections to %d due to low utilization", newMax)
	}

	// Adjust idle connections based on usage
	if stats.Idle > stats.MaxIdleConns && stats.InUse < 5 {
		m.sqlDB.SetMaxIdleConns(stats.MaxIdleConns - 2)
	} else if stats.Idle == 0 && stats.InUse > 10 {
		m.sqlDB.SetMaxIdleConns(stats.MaxIdleConns + 2)
	}
}

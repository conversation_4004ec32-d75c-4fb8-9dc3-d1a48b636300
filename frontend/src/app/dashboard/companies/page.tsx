"use client";

import { useState, useCallback, useMemo } from "react";
import { useCompanies } from "@/lib/react-query/hooks";
import { useDebounce } from "@/lib/hooks/use-debounce";
import { Card, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { SearchFilterBar } from "@/components/ui/search-filter-bar";
import { 
  Plus, TrendingUp, Building, Briefcase, Factory 
} from "lucide-react";
import { AddCompanyDialog } from "@/components/companies/add-company-dialog";
import { EditCompanyDialog } from "@/components/companies/edit-company-dialog";
import { DeleteCompanyDialog } from "@/components/companies/delete-company-dialog";
import { CompanyCard } from "@/components/companies/company-card";
import { Company } from "@/types";

export default function CompaniesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<any>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [addCompanyOpen, setAddCompanyOpen] = useState(false);
  const [editCompanyOpen, setEditCompanyOpen] = useState(false);
  const [deleteCompanyOpen, setDeleteCompanyOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  
  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  
  // Build query params from filters with debounced search
  const queryParams = useMemo(() => ({
    q: debouncedSearchQuery,
    page: currentPage,
    limit: 20,
    ...(filters.sortBy && { sort: filters.sortBy }),
    ...(filters.categories?.length && { industry: filters.categories.join(',') }),
    ...(filters.statuses?.length && { status: filters.statuses.join(',') }),
    ...(filters.dateRange?.from && { from: filters.dateRange.from.toISOString() }),
    ...(filters.dateRange?.to && { to: filters.dateRange.to.toISOString() }),
  }), [debouncedSearchQuery, currentPage, filters]);
  
  const { data: companies, isLoading, error } = useCompanies(queryParams);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  }, []);

  const handleFiltersChange = useCallback((newFilters: any) => {
    setFilters(newFilters);
    setCurrentPage(1);
  }, []);

  const handleEditCompany = (company: Company) => {
    setSelectedCompany(company);
    setEditCompanyOpen(true);
  };

  const handleDeleteCompany = (company: Company) => {
    setSelectedCompany(company);
    setDeleteCompanyOpen(true);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Companies</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">Companies</h1>
        <div className="bg-destructive/15 border border-destructive/20 rounded-md p-4">
          <p className="text-destructive">Failed to load companies</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Компании</h1>
          <p className="text-gray-600 mt-1">
            Управление компаниями и организациями
          </p>
        </div>
        <Button onClick={() => setAddCompanyOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Добавить компанию
        </Button>
      </div>

      {/* Search and Filters */}
      <SearchFilterBar
        placeholder="Поиск компаний по названию, сайту, отрасли..."
        searchValue={searchQuery}
        onSearch={handleSearch}
        onFiltersChange={handleFiltersChange}
        onExport={() => console.log("Export companies")}
        onImport={() => console.log("Import companies")}
        totalCount={companies?.length}
        sortOptions={[
          { value: "name_asc", label: "Название (А-Я)" },
          { value: "name_desc", label: "Название (Я-А)" },
          { value: "size_asc", label: "Размер (малые сначала)" },
          { value: "size_desc", label: "Размер (большие сначала)" },
          { value: "created_desc", label: "Сначала новые" },
          { value: "updated_desc", label: "Недавно обновленные" },
        ]}
        categoryOptions={[
          { value: "technology", label: "Технологии", icon: <TrendingUp className="h-4 w-4 mr-2" /> },
          { value: "consulting", label: "Консалтинг", icon: <Briefcase className="h-4 w-4 mr-2" /> },
          { value: "manufacturing", label: "Производство", icon: <Factory className="h-4 w-4 mr-2" /> },
          { value: "services", label: "Услуги", icon: <Building className="h-4 w-4 mr-2" /> },
          { value: "retail", label: "Розничная торговля", icon: <Building className="h-4 w-4 mr-2" /> },
          { value: "finance", label: "Финансы", icon: <TrendingUp className="h-4 w-4 mr-2" /> },
        ]}
        statusOptions={[
          { value: "active", label: "Активная" },
          { value: "prospect", label: "Потенциальная" },
          { value: "inactive", label: "Неактивная" },
          { value: "partner", label: "Партнер" },
        ]}
      />

      {/* Companies Grid */}
      {companies && companies.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {companies.map((company) => (
            <CompanyCard
              key={company.id}
              company={company}
              onEdit={handleEditCompany}
              onDelete={handleDeleteCompany}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">Компании не найдены</p>
          <p className="text-gray-400 mt-2">
            {searchQuery ? "Попробуйте изменить параметры поиска" : "Создайте первую компанию, чтобы начать"}
          </p>
          <Button className="mt-4" onClick={() => setAddCompanyOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Добавить компанию
          </Button>
        </div>
      )}

      {/* Pagination would go here */}

      {/* Dialogs */}
      <AddCompanyDialog
        open={addCompanyOpen}
        onOpenChange={setAddCompanyOpen}
      />

      <EditCompanyDialog
        company={selectedCompany}
        open={editCompanyOpen}
        onOpenChange={setEditCompanyOpen}
      />

      <DeleteCompanyDialog
        company={selectedCompany}
        open={deleteCompanyOpen}
        onOpenChange={setDeleteCompanyOpen}
      />
    </div>
  );
}
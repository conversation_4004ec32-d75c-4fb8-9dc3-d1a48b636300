"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo, memo } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Shield,
  Plus,
  Edit,
  Trash2,
  Save,
  Copy,
  Users,
  FileText,
  Building2,
  CheckSquare,
  Square,
  Minus,
  Eye,
  Edit2,
  Trash,
  Download,
  Upload,
  History,
  Info,
  Lock,
  Unlock,
  UserCheck,
  Settings,
  ChevronDown,
  ChevronRight,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { OptimizedCheckbox } from "@/components/ui/optimized-checkbox";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { useRoles, useRole } from "@/hooks/useRoles";
import { CreateRoleData } from "@/lib/api/roles";

// Resource mapping for UI display
const resourceMapping = {
  cards: { label: "Карточки/Сделки", icon: <FileText className="h-4 w-4" /> },
  contacts: { label: "Контакты", icon: <Users className="h-4 w-4" /> },
  companies: { label: "Компании", icon: <Building2 className="h-4 w-4" /> },
  users: { label: "Пользователи", icon: <UserCheck className="h-4 w-4" /> },
  pipelines: { label: "Воронки", icon: <Settings className="h-4 w-4" /> },
  tasks: { label: "Задачи", icon: <CheckSquare className="h-4 w-4" /> },
  reports: { label: "Отчеты", icon: <FileText className="h-4 w-4" /> },
  settings: { label: "Настройки", icon: <Settings className="h-4 w-4" /> },
};

// Action mapping for UI display
const actionMapping = {
  create: { label: "Создание", icon: <Plus className="h-4 w-4" /> },
  read: { label: "Просмотр", icon: <Eye className="h-4 w-4" /> },
  update: { label: "Редактирование", icon: <Edit2 className="h-4 w-4" /> },
  delete: { label: "Удаление", icon: <Trash className="h-4 w-4" /> },
  export: { label: "Экспорт", icon: <Download className="h-4 w-4" /> },
  import: { label: "Импорт", icon: <Upload className="h-4 w-4" /> },
  bulk: { label: "Массовые операции", icon: <CheckSquare className="h-4 w-4" /> },
  all: { label: "Все записи", icon: <Users className="h-4 w-4" /> },
};

export default function RolesSettingsPage() {
  const router = useRouter();
  const [selectedRoleId, setSelectedRoleId] = useState<string>("");
  const [isAddRoleOpen, setIsAddRoleOpen] = useState(false);
  const [isDuplicateOpen, setIsDuplicateOpen] = useState(false);
  const [duplicateRoleId, setDuplicateRoleId] = useState<string>("");
  const [duplicateName, setDuplicateName] = useState("");
  const [viewMode, setViewMode] = useState<"matrix" | "list">("matrix");
  const [localPermissions, setLocalPermissions] = useState<string[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const [newRole, setNewRole] = useState<CreateRoleData>({
    name: "",
    description: "",
    color: "#3b82f6",
  });

  // Load roles and permissions
  const {
    roles,
    permissions,
    isLoading: rolesLoading,
    permissionsLoading,
    createRole,
    updateRole,
    deleteRole,
    duplicateRole,
    updateRolePermissions,
    isCreating,
    isUpdating,
    isDeleting,
    isDuplicating,
    isUpdatingPermissions,
    getPermissionsByResource,
  } = useRoles();

  // Load specific role details
  const {
    role: selectedRole,
    rolePermissions,
    isLoading: roleLoading,
  } = useRole(selectedRoleId);

  // Set first role as selected when roles load
  useEffect(() => {
    if (roles.length > 0 && !selectedRoleId) {
      setSelectedRoleId(roles[0].id);
    }
  }, [roles, selectedRoleId]);

  // Sync local permissions with backend permissions when role changes
  useEffect(() => {
    if (rolePermissions) {
      setLocalPermissions(rolePermissions.map(p => p.id));
      setHasUnsavedChanges(false);
    }
  }, [selectedRoleId]); // Only depend on selectedRoleId to avoid infinite loops

  // Group permissions by resource
  const permissionsByResource = permissions.reduce((acc, permission) => {
    if (!acc[permission.resource]) {
      acc[permission.resource] = [];
    }
    acc[permission.resource].push(permission);
    return acc;
  }, {} as Record<string, typeof permissions>);

  // Get available resources and actions
  const availableResources = Object.keys(permissionsByResource);
  const availableActions = [...new Set(permissions.map(p => p.action))];

  const handleCreateRole = async () => {
    if (!newRole.name.trim()) {
      toast({
        title: "Ошибка",
        description: "Введите название роли",
        variant: "destructive",
      });
      return;
    }

    createRole(newRole, {
      onSuccess: () => {
        setIsAddRoleOpen(false);
        setNewRole({ name: "", description: "", color: "#3b82f6" });
      },
    });
  };

  const handleDuplicateRole = async () => {
    if (!duplicateName.trim()) {
      toast({
        title: "Ошибка",
        description: "Введите название для копии роли",
        variant: "destructive",
      });
      return;
    }

    duplicateRole({ id: duplicateRoleId, newName: duplicateName }, {
      onSuccess: () => {
        setIsDuplicateOpen(false);
        setDuplicateName("");
        setDuplicateRoleId("");
      },
    });
  };

  const handleDeleteRole = async (roleId: string) => {
    const role = roles.find(r => r.id === roleId);
    if (!role) return;

    if (role.is_system) {
      toast({
        title: "Ошибка",
        description: "Системную роль нельзя удалить",
        variant: "destructive",
      });
      return;
    }

    if (!confirm(`Удалить роль "${role.name}"?`)) return;

    deleteRole(roleId, {
      onSuccess: () => {
        if (selectedRoleId === roleId && roles.length > 1) {
          const otherRole = roles.find(r => r.id !== roleId);
          if (otherRole) setSelectedRoleId(otherRole.id);
        }
      },
    });
  };

  const handlePermissionToggle = useCallback((permissionId: string, checked: boolean) => {
    if (!selectedRole || selectedRole.is_system) return;

    setLocalPermissions(prev => {
      let newPermissionIds: string[];
      if (checked) {
        newPermissionIds = [...prev, permissionId];
      } else {
        newPermissionIds = prev.filter(id => id !== permissionId);
      }
      return newPermissionIds;
    });
    setHasUnsavedChanges(true);
  }, [selectedRole]);

  const handleResourceToggle = useCallback((resource: string, checked: boolean) => {
    if (!selectedRole || selectedRole.is_system) return;

    const resourcePermissions = permissionsByResource[resource] || [];
    
    setLocalPermissions(prev => {
      let newPermissionIds: string[];
      if (checked) {
        const resourcePermissionIds = resourcePermissions.map(p => p.id);
        newPermissionIds = [...new Set([...prev, ...resourcePermissionIds])];
      } else {
        const resourcePermissionIds = resourcePermissions.map(p => p.id);
        newPermissionIds = prev.filter(id => !resourcePermissionIds.includes(id));
      }
      return newPermissionIds;
    });
    setHasUnsavedChanges(true);
  }, [selectedRole, permissionsByResource]);

  const handleActionToggle = useCallback((action: string, checked: boolean) => {
    if (!selectedRole || selectedRole.is_system) return;

    const actionPermissions = permissions.filter(p => p.action === action);
    
    setLocalPermissions(prev => {
      let newPermissionIds: string[];
      if (checked) {
        const actionPermissionIds = actionPermissions.map(p => p.id);
        newPermissionIds = [...new Set([...prev, ...actionPermissionIds])];
      } else {
        const actionPermissionIds = actionPermissions.map(p => p.id);
        newPermissionIds = prev.filter(id => !actionPermissionIds.includes(id));
      }
      return newPermissionIds;
    });
    setHasUnsavedChanges(true);
  }, [selectedRole, permissions]);

  const handleSavePermissions = () => {
    if (!selectedRole || !hasUnsavedChanges) return;
    
    updateRolePermissions({ id: selectedRole.id, permissionIds: localPermissions }, {
      onSuccess: () => {
        setHasUnsavedChanges(false);
      }
    });
  };

  const handleCancelChanges = () => {
    if (rolePermissions) {
      setLocalPermissions(rolePermissions.map(p => p.id));
      setHasUnsavedChanges(false);
    }
  };

  // Check if permission is granted
  const hasPermission = useCallback((resource: string, action: string) => {
    const permission = permissions.find(p => p.resource === resource && p.action === action);
    return permission ? localPermissions.includes(permission.id) : false;
  }, [permissions, localPermissions]);

  // Check resource permission state
  const getResourceState = useCallback((resource: string) => {
    const resourcePerms = permissionsByResource[resource] || [];
    const grantedPerms = resourcePerms.filter(p => localPermissions.includes(p.id));
    
    if (grantedPerms.length === 0) return "none";
    if (grantedPerms.length === resourcePerms.length) return "all";
    return "partial";
  }, [permissionsByResource, localPermissions]);

  // Check action permission state
  const getActionState = useCallback((action: string) => {
    const actionPerms = permissions.filter(p => p.action === action);
    const grantedPerms = actionPerms.filter(p => localPermissions.includes(p.id));
    
    if (grantedPerms.length === 0) return "none";
    if (grantedPerms.length === actionPerms.length) return "all";
    return "partial";
  }, [permissions, localPermissions]);

  if (rolesLoading || permissionsLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.push("/dashboard/settings")}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-3">
                <Shield className="h-8 w-8" />
                Роли и права доступа
              </h1>
              <p className="text-gray-500 mt-1">Настройка прав доступа для ролей пользователей</p>
            </div>
          </div>
        </div>
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (roles.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.push("/dashboard/settings")}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-3">
                <Shield className="h-8 w-8" />
                Роли и права доступа
              </h1>
              <p className="text-gray-500 mt-1">Настройка прав доступа для ролей пользователей</p>
            </div>
          </div>
        </div>
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">Роли не найдены</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push("/dashboard/settings")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Shield className="h-8 w-8" />
              Роли и права доступа
            </h1>
            <p className="text-gray-500 mt-1">Настройка прав доступа для ролей пользователей</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-12 gap-6">
        {/* Список ролей */}
        <div className="col-span-3">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Роли ({roles.length})</CardTitle>
                <Button size="sm" onClick={() => setIsAddRoleOpen(true)} disabled={isCreating}>
                  {isCreating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-2">
              {roles.map((role) => (
                <div
                  key={role.id}
                  className={cn(
                    "p-3 rounded-lg border cursor-pointer transition-all",
                    selectedRoleId === role.id
                      ? "border-primary bg-primary/5"
                      : "hover:bg-gray-50 dark:hover:bg-gray-800"
                  )}
                  onClick={() => setSelectedRoleId(role.id)}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{role.name}</h4>
                        {role.is_system && (
                          <Badge variant="secondary" className="text-xs">
                            <Lock className="h-3 w-3 mr-1" />
                            Системная
                          </Badge>
                        )}
                        {!role.is_active && (
                          <Badge variant="outline" className="text-xs text-red-500">
                            Неактивная
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500 mt-1">{role.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline" className="text-xs">
                          <Users className="h-3 w-3 mr-1" />
                          {role.user_count || 0} польз.
                        </Badge>
                      </div>
                    </div>
                    {selectedRoleId === role.id && (
                      <div className="flex gap-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-8 w-8"
                                disabled={isDuplicating}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setDuplicateRoleId(role.id);
                                  setDuplicateName(`${role.name} (копия)`);
                                  setIsDuplicateOpen(true);
                                }}
                              >
                                {isDuplicating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Copy className="h-4 w-4" />}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Дублировать роль</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        {!role.is_system && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  className="h-8 w-8 text-red-500 hover:text-red-600"
                                  disabled={isDeleting}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteRole(role.id);
                                  }}
                                >
                                  {isDeleting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Удалить роль</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Матрица прав */}
        <div className="col-span-9">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>
                    Права доступа: {selectedRole?.name}
                    {isUpdatingPermissions && <Loader2 className="h-4 w-4 animate-spin inline ml-2" />}
                  </CardTitle>
                  <CardDescription>{selectedRole?.description}</CardDescription>
                </div>
                <div className="flex gap-2">
                  {hasUnsavedChanges && (
                    <>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={handleSavePermissions}
                        disabled={isUpdatingPermissions}
                      >
                        {isUpdatingPermissions ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                        Сохранить
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancelChanges}
                        disabled={isUpdatingPermissions}
                      >
                        Отмена
                      </Button>
                      <div className="h-8 w-px bg-border" />
                    </>
                  )}
                  <Button
                    variant={viewMode === "matrix" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("matrix")}
                  >
                    Матрица
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                  >
                    Список
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {roleLoading ? (
                <div className="flex justify-center items-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : selectedRole ? (
                <div className="space-y-4">
                  {viewMode === "matrix" ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-48">Ресурс</TableHead>
                            {availableActions.map((action) => (
                              <TableHead key={action} className="text-center w-24">
                                <div className="flex flex-col items-center gap-1">
                                  {actionMapping[action as keyof typeof actionMapping]?.icon || <Square className="h-4 w-4" />}
                                  <span className="text-xs">{actionMapping[action as keyof typeof actionMapping]?.label || action}</span>
                                </div>
                              </TableHead>
                            ))}
                            <TableHead className="text-center w-24">
                              <div className="flex flex-col items-center gap-1">
                                <CheckSquare className="h-4 w-4" />
                                <span className="text-xs">Все</span>
                              </div>
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {availableResources.map((resource) => {
                            const state = getResourceState(resource);
                            return (
                              <TableRow key={resource}>
                                <TableCell>
                                  <div className="flex items-center gap-2 font-medium">
                                    {resourceMapping[resource as keyof typeof resourceMapping]?.icon || <Square className="h-4 w-4" />}
                                    {resourceMapping[resource as keyof typeof resourceMapping]?.label || resource}
                                  </div>
                                </TableCell>
                                {availableActions.map((action) => (
                                  <TableCell key={action} className="text-center">
                                    <OptimizedCheckbox
                                      checked={hasPermission(resource, action)}
                                      disabled={selectedRole.is_system || isUpdatingPermissions}
                                      onCheckedChange={(checked) => {
                                        const permission = permissions.find(p => p.resource === resource && p.action === action);
                                        if (permission) {
                                          handlePermissionToggle(permission.id, checked);
                                        }
                                      }}
                                      optimistic={true}
                                    />
                                  </TableCell>
                                ))}
                                <TableCell className="text-center">
                                  <OptimizedCheckbox
                                    checked={state === "all"}
                                    indeterminate={state === "partial"}
                                    disabled={selectedRole.is_system || isUpdatingPermissions}
                                    onCheckedChange={(checked) =>
                                      handleResourceToggle(resource, checked)
                                    }
                                    optimistic={true}
                                  />
                                </TableCell>
                              </TableRow>
                            );
                          })}
                          <TableRow>
                            <TableCell className="font-medium">
                              Выбрать все
                            </TableCell>
                            {availableActions.map((action) => {
                              const state = getActionState(action);
                              return (
                                <TableCell key={action} className="text-center">
                                  <OptimizedCheckbox
                                    checked={state === "all"}
                                    indeterminate={state === "partial"}
                                    disabled={selectedRole.is_system || isUpdatingPermissions}
                                    onCheckedChange={(checked) =>
                                      handleActionToggle(action, checked)
                                    }
                                    optimistic={true}
                                  />
                                </TableCell>
                              );
                            })}
                            <TableCell className="text-center">
                              <OptimizedCheckbox
                                checked={localPermissions.length === permissions.length}
                                disabled={selectedRole.is_system || isUpdatingPermissions}
                                onCheckedChange={(checked) => {
                                  const allPermissionIds = checked ? permissions.map(p => p.id) : [];
                                  setLocalPermissions(allPermissionIds);
                                  setHasUnsavedChanges(true);
                                }}
                                optimistic={true}
                              />
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {availableResources.map((resource) => (
                        <Card key={resource}>
                          <CardHeader>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                {resourceMapping[resource as keyof typeof resourceMapping]?.icon || <Square className="h-4 w-4" />}
                                <CardTitle className="text-lg">{resourceMapping[resource as keyof typeof resourceMapping]?.label || resource}</CardTitle>
                              </div>
                              <Switch
                                checked={getResourceState(resource) === "all"}
                                disabled={selectedRole.is_system || isUpdatingPermissions}
                                onCheckedChange={(checked) => handleResourceToggle(resource, checked)}
                              />
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                              {(permissionsByResource[resource] || []).map((permission) => (
                                <div key={permission.id} className="flex items-center justify-between p-3 border rounded-lg">
                                  <div>
                                    <p className="font-medium text-sm">{actionMapping[permission.action as keyof typeof actionMapping]?.label || permission.action}</p>
                                    <p className="text-xs text-gray-500">{permission.description}</p>
                                  </div>
                                  <OptimizedCheckbox
                                    checked={localPermissions.includes(permission.id)}
                                    disabled={selectedRole.is_system || isUpdatingPermissions}
                                    onCheckedChange={(checked) => 
                                      handlePermissionToggle(permission.id, checked)
                                    }
                                    optimistic={true}
                                  />
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Выберите роль для просмотра прав доступа</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Создание новой роли */}
      <Dialog open={isAddRoleOpen} onOpenChange={setIsAddRoleOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Создать роль</DialogTitle>
            <DialogDescription>
              Создайте новую роль с настраиваемыми правами
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="roleName">Название роли</Label>
              <Input
                id="roleName"
                value={newRole.name}
                onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                placeholder="Например: Руководитель отдела"
              />
            </div>
            <div>
              <Label htmlFor="roleDescription">Описание</Label>
              <Textarea
                id="roleDescription"
                value={newRole.description}
                onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                placeholder="Описание роли и её назначения"
              />
            </div>
            <div>
              <Label htmlFor="roleColor">Цвет</Label>
              <Input
                id="roleColor"
                type="color"
                value={newRole.color}
                onChange={(e) => setNewRole({ ...newRole, color: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddRoleOpen(false)} disabled={isCreating}>
              Отмена
            </Button>
            <Button onClick={handleCreateRole} disabled={isCreating}>
              {isCreating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Создать роль
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Дублирование роли */}
      <Dialog open={isDuplicateOpen} onOpenChange={setIsDuplicateOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Дублировать роль</DialogTitle>
            <DialogDescription>
              Создать копию роли с теми же правами
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="duplicateName">Название копии</Label>
              <Input
                id="duplicateName"
                value={duplicateName}
                onChange={(e) => setDuplicateName(e.target.value)}
                placeholder="Название для копии роли"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDuplicateOpen(false)} disabled={isDuplicating}>
              Отмена
            </Button>
            <Button onClick={handleDuplicateRole} disabled={isDuplicating}>
              {isDuplicating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Дублировать
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Settings,
  Users,
  Shield,
  Bell,
  Palette,
  Globe,
  Key,
  Database,
  FileText,
  Building,
  UserCheck,
  Lock,
  Zap,
  Mail,
  Smartphone,
  CreditCard,
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

const settingSections = [
  {
    title: "Организация",
    items: [
      { icon: Building, label: "Общие настройки", href: "/dashboard/settings/general", description: "Название, логотип, реквизиты" },
      { icon: Globe, label: "Регион и язык", href: "/dashboard/settings/locale", description: "Часовой пояс, язык, валюта" },
      { icon: Palette, label: "Оформление", href: "/dashboard/settings/appearance", description: "Тема, цвета, логотип" },
    ]
  },
  {
    title: "Пользователи и доступ",
    items: [
      { icon: Users, label: "Пользователи", href: "/dashboard/settings/users", description: "Управление пользователями" },
      { icon: UserCheck, label: "Роли и права", href: "/dashboard/settings/roles", description: "Матрица прав доступа" },
      { icon: Shield, label: "Безопасность", href: "/dashboard/settings/security", description: "Пароли, 2FA, сессии" },
    ]
  },
  {
    title: "Бизнес-процессы",
    items: [
      { icon: Zap, label: "Автоматизация", href: "/dashboard/settings/automation", description: "Триггеры и правила" },
      { icon: FileText, label: "Шаблоны", href: "/dashboard/settings/templates", description: "Документы и письма" },
      { icon: Database, label: "Поля и формы", href: "/dashboard/settings/fields", description: "Пользовательские поля" },
    ]
  },
  {
    title: "Коммуникации",
    items: [
      { icon: Bell, label: "Уведомления", href: "/dashboard/settings/notifications", description: "Email, push, SMS" },
      { icon: Mail, label: "Email", href: "/dashboard/settings/email", description: "Почтовые ящики и рассылки" },
      { icon: Smartphone, label: "Телефония", href: "/dashboard/settings/telephony", description: "IP-телефония, записи" },
    ]
  },
  {
    title: "Интеграции",
    items: [
      { icon: Key, label: "API и Webhooks", href: "/dashboard/settings/api", description: "Ключи доступа, endpoints" },
      { icon: CreditCard, label: "Платежи", href: "/dashboard/settings/payments", description: "Платежные системы" },
      { icon: Lock, label: "OAuth приложения", href: "/dashboard/settings/oauth", description: "Подключенные сервисы" },
    ]
  },
];

export default function SettingsPage() {
  const router = useRouter();
  const [selectedSection, setSelectedSection] = useState("");

  const handleNavigate = (href: string) => {
    router.push(href);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold flex items-center gap-3">
          <Settings className="h-8 w-8" />
          Настройки системы
        </h1>
        <p className="text-gray-500 mt-1">Управление организацией, пользователями и бизнес-процессами</p>
      </div>

      <div className="grid gap-8">
        {settingSections.map((section) => (
          <div key={section.title}>
            <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">
              {section.title}
            </h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {section.items.map((item) => {
                const Icon = item.icon;
                return (
                  <Card
                    key={item.href}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-lg hover:scale-105",
                      "hover:border-primary"
                    )}
                    onClick={() => handleNavigate(item.href)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-primary/10">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <CardTitle className="text-base">{item.label}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription>{item.description}</CardDescription>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
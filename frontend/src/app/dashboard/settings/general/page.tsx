"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Building,
  Upload,
  Save,
  Globe,
  Phone,
  Mail,
  MapPin,
  FileText,
  CreditCard,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/hooks/use-toast";
import { apiClient } from "@/lib/api";

interface OrganizationSettings {
  // Основная информация
  name: string;
  legalName: string;
  logo: string;
  favicon: string;
  description: string;
  industry: string;
  size: string;
  foundedYear: string;
  
  // Контактная информация
  email: string;
  phone: string;
  website: string;
  address: string;
  city: string;
  country: string;
  postalCode: string;
  
  // Реквизиты
  taxId: string;
  vatId: string;
  registrationNumber: string;
  bankName: string;
  bankAccount: string;
  bankRoutingNumber: string;
  
  // Региональные настройки
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  currency: string;
  language: string;
  firstDayOfWeek: string;
  
  // Дополнительные настройки
  fiscalYearStart: string;
  workingDays: string[];
  businessHours: {
    start: string;
    end: string;
  };
}

const industries = [
  "IT и технологии",
  "Финансы и банки",
  "Производство",
  "Розничная торговля",
  "Оптовая торговля",
  "Недвижимость",
  "Строительство",
  "Транспорт и логистика",
  "Здравоохранение",
  "Образование",
  "Консалтинг",
  "Маркетинг и реклама",
  "Другое",
];

const companySizes = [
  "1-10 сотрудников",
  "11-50 сотрудников",
  "51-200 сотрудников",
  "201-500 сотрудников",
  "501-1000 сотрудников",
  "1000+ сотрудников",
];

const timezones = [
  { value: "Europe/Moscow", label: "Москва (UTC+3)" },
  { value: "Europe/Kaliningrad", label: "Калининград (UTC+2)" },
  { value: "Asia/Yekaterinburg", label: "Екатеринбург (UTC+5)" },
  { value: "Asia/Novosibirsk", label: "Новосибирск (UTC+7)" },
  { value: "Asia/Krasnoyarsk", label: "Красноярск (UTC+7)" },
  { value: "Asia/Vladivostok", label: "Владивосток (UTC+10)" },
];

const currencies = [
  { value: "RUB", label: "₽ Российский рубль" },
  { value: "USD", label: "$ Доллар США" },
  { value: "EUR", label: "€ Евро" },
  { value: "GBP", label: "£ Фунт стерлингов" },
  { value: "CNY", label: "¥ Китайский юань" },
];

export default function GeneralSettingsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  
  const [settings, setSettings] = useState<OrganizationSettings>({
    name: "Название компании",
    legalName: "",
    logo: "",
    favicon: "",
    description: "",
    industry: "",
    size: "",
    foundedYear: "",
    email: "",
    phone: "",
    website: "",
    address: "",
    city: "",
    country: "Россия",
    postalCode: "",
    taxId: "",
    vatId: "",
    registrationNumber: "",
    bankName: "",
    bankAccount: "",
    bankRoutingNumber: "",
    timezone: "Europe/Moscow",
    dateFormat: "DD.MM.YYYY",
    timeFormat: "24h",
    currency: "RUB",
    language: "ru",
    firstDayOfWeek: "monday",
    fiscalYearStart: "january",
    workingDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
    businessHours: {
      start: "09:00",
      end: "18:00",
    },
  });

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      // В реальном приложении здесь будет запрос к API
      // const response = await apiClient.get("/api/v1/settings/organization");
      // setSettings(response.data);
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось загрузить настройки",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      // const response = await apiClient.put("/api/v1/settings/organization", settings);
      toast({
        title: "Успешно",
        description: "Настройки сохранены",
      });
      setHasChanges(false);
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось сохранить настройки",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const updateSettings = (updates: Partial<OrganizationSettings>) => {
    setSettings({ ...settings, ...updates });
    setHasChanges(true);
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // В реальном приложении здесь будет загрузка файла
      const reader = new FileReader();
      reader.onloadend = () => {
        updateSettings({ logo: reader.result as string });
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push("/dashboard/settings")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Building className="h-8 w-8" />
              Общие настройки
            </h1>
            <p className="text-gray-500 mt-1">Настройки организации и региональные параметры</p>
          </div>
        </div>
        {hasChanges && (
          <Button onClick={handleSave} disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? "Сохранение..." : "Сохранить изменения"}
          </Button>
        )}
      </div>

      <Tabs defaultValue="organization" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="organization">Организация</TabsTrigger>
          <TabsTrigger value="contacts">Контакты</TabsTrigger>
          <TabsTrigger value="requisites">Реквизиты</TabsTrigger>
          <TabsTrigger value="regional">Региональные</TabsTrigger>
        </TabsList>

        <TabsContent value="organization" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Основная информация</CardTitle>
              <CardDescription>Общие сведения о вашей организации</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Название организации</Label>
                  <Input
                    id="name"
                    value={settings.name}
                    onChange={(e) => updateSettings({ name: e.target.value })}
                    placeholder="ООО «Компания»"
                  />
                </div>
                <div>
                  <Label htmlFor="legalName">Юридическое название</Label>
                  <Input
                    id="legalName"
                    value={settings.legalName}
                    onChange={(e) => updateSettings({ legalName: e.target.value })}
                    placeholder="Общество с ограниченной ответственностью «Компания»"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Описание</Label>
                <Textarea
                  id="description"
                  value={settings.description}
                  onChange={(e) => updateSettings({ description: e.target.value })}
                  placeholder="Краткое описание вашей организации"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="industry">Отрасль</Label>
                  <Select
                    value={settings.industry}
                    onValueChange={(value) => updateSettings({ industry: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Выберите отрасль" />
                    </SelectTrigger>
                    <SelectContent>
                      {industries.map((industry) => (
                        <SelectItem key={industry} value={industry}>
                          {industry}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="size">Размер компании</Label>
                  <Select
                    value={settings.size}
                    onValueChange={(value) => updateSettings({ size: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Количество сотрудников" />
                    </SelectTrigger>
                    <SelectContent>
                      {companySizes.map((size) => (
                        <SelectItem key={size} value={size}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="foundedYear">Год основания</Label>
                  <Input
                    id="foundedYear"
                    type="number"
                    value={settings.foundedYear}
                    onChange={(e) => updateSettings({ foundedYear: e.target.value })}
                    placeholder="2020"
                  />
                </div>
              </div>

              <Separator />

              <div>
                <Label>Логотип компании</Label>
                <div className="flex items-center gap-4 mt-2">
                  {settings.logo ? (
                    <img
                      src={settings.logo}
                      alt="Logo"
                      className="h-20 w-20 object-contain border rounded-lg"
                    />
                  ) : (
                    <div className="h-20 w-20 border-2 border-dashed rounded-lg flex items-center justify-center">
                      <Upload className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                  <div>
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                      id="logo-upload"
                    />
                    <Label htmlFor="logo-upload" className="cursor-pointer">
                      <Button variant="outline" asChild>
                        <span>
                          <Upload className="mr-2 h-4 w-4" />
                          Загрузить логотип
                        </span>
                      </Button>
                    </Label>
                    <p className="text-sm text-gray-500 mt-1">PNG, JPG до 5MB</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contacts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Контактная информация</CardTitle>
              <CardDescription>Контактные данные для связи с организацией</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">
                    <Mail className="inline h-4 w-4 mr-1" />
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={settings.email}
                    onChange={(e) => updateSettings({ email: e.target.value })}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="phone">
                    <Phone className="inline h-4 w-4 mr-1" />
                    Телефон
                  </Label>
                  <Input
                    id="phone"
                    value={settings.phone}
                    onChange={(e) => updateSettings({ phone: e.target.value })}
                    placeholder="+7 (495) 123-45-67"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="website">
                  <Globe className="inline h-4 w-4 mr-1" />
                  Веб-сайт
                </Label>
                <Input
                  id="website"
                  value={settings.website}
                  onChange={(e) => updateSettings({ website: e.target.value })}
                  placeholder="https://company.ru"
                />
              </div>

              <Separator />

              <div>
                <Label htmlFor="address">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Адрес
                </Label>
                <Input
                  id="address"
                  value={settings.address}
                  onChange={(e) => updateSettings({ address: e.target.value })}
                  placeholder="ул. Примерная, д. 1, офис 100"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="city">Город</Label>
                  <Input
                    id="city"
                    value={settings.city}
                    onChange={(e) => updateSettings({ city: e.target.value })}
                    placeholder="Москва"
                  />
                </div>
                <div>
                  <Label htmlFor="country">Страна</Label>
                  <Input
                    id="country"
                    value={settings.country}
                    onChange={(e) => updateSettings({ country: e.target.value })}
                    placeholder="Россия"
                  />
                </div>
                <div>
                  <Label htmlFor="postalCode">Почтовый индекс</Label>
                  <Input
                    id="postalCode"
                    value={settings.postalCode}
                    onChange={(e) => updateSettings({ postalCode: e.target.value })}
                    placeholder="123456"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="requisites" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Юридические реквизиты</CardTitle>
              <CardDescription>Реквизиты для документов и платежей</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="taxId">
                    <FileText className="inline h-4 w-4 mr-1" />
                    ИНН
                  </Label>
                  <Input
                    id="taxId"
                    value={settings.taxId}
                    onChange={(e) => updateSettings({ taxId: e.target.value })}
                    placeholder="*********0"
                  />
                </div>
                <div>
                  <Label htmlFor="vatId">КПП</Label>
                  <Input
                    id="vatId"
                    value={settings.vatId}
                    onChange={(e) => updateSettings({ vatId: e.target.value })}
                    placeholder="*********"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="registrationNumber">ОГРН/ОГРНИП</Label>
                <Input
                  id="registrationNumber"
                  value={settings.registrationNumber}
                  onChange={(e) => updateSettings({ registrationNumber: e.target.value })}
                  placeholder="*********0123"
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Банковские реквизиты
                </h4>
                <div>
                  <Label htmlFor="bankName">Наименование банка</Label>
                  <Input
                    id="bankName"
                    value={settings.bankName}
                    onChange={(e) => updateSettings({ bankName: e.target.value })}
                    placeholder="ПАО «Сбербанк»"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="bankAccount">Расчетный счет</Label>
                    <Input
                      id="bankAccount"
                      value={settings.bankAccount}
                      onChange={(e) => updateSettings({ bankAccount: e.target.value })}
                      placeholder="40702810000000000000"
                    />
                  </div>
                  <div>
                    <Label htmlFor="bankRoutingNumber">БИК банка</Label>
                    <Input
                      id="bankRoutingNumber"
                      value={settings.bankRoutingNumber}
                      onChange={(e) => updateSettings({ bankRoutingNumber: e.target.value })}
                      placeholder="*********"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="regional" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Региональные настройки</CardTitle>
              <CardDescription>Часовой пояс, язык, валюта и форматы</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="timezone">Часовой пояс</Label>
                  <Select
                    value={settings.timezone}
                    onValueChange={(value) => updateSettings({ timezone: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {timezones.map((tz) => (
                        <SelectItem key={tz.value} value={tz.value}>
                          {tz.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="currency">Валюта по умолчанию</Label>
                  <Select
                    value={settings.currency}
                    onValueChange={(value) => updateSettings({ currency: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {currencies.map((currency) => (
                        <SelectItem key={currency.value} value={currency.value}>
                          {currency.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="dateFormat">Формат даты</Label>
                  <Select
                    value={settings.dateFormat}
                    onValueChange={(value) => updateSettings({ dateFormat: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DD.MM.YYYY">31.12.2024</SelectItem>
                      <SelectItem value="MM/DD/YYYY">12/31/2024</SelectItem>
                      <SelectItem value="YYYY-MM-DD">2024-12-31</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="timeFormat">Формат времени</Label>
                  <Select
                    value={settings.timeFormat}
                    onValueChange={(value) => updateSettings({ timeFormat: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="24h">24-часовой (15:30)</SelectItem>
                      <SelectItem value="12h">12-часовой (3:30 PM)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="firstDayOfWeek">Первый день недели</Label>
                  <Select
                    value={settings.firstDayOfWeek}
                    onValueChange={(value) => updateSettings({ firstDayOfWeek: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monday">Понедельник</SelectItem>
                      <SelectItem value="sunday">Воскресенье</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div>
                <Label>Рабочие дни</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"].map((day) => {
                    const dayLabels = {
                      monday: "Пн",
                      tuesday: "Вт",
                      wednesday: "Ср",
                      thursday: "Чт",
                      friday: "Пт",
                      saturday: "Сб",
                      sunday: "Вс",
                    };
                    const isWorkingDay = settings.workingDays.includes(day);
                    return (
                      <Badge
                        key={day}
                        variant={isWorkingDay ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => {
                          const newWorkingDays = isWorkingDay
                            ? settings.workingDays.filter((d) => d !== day)
                            : [...settings.workingDays, day];
                          updateSettings({ workingDays: newWorkingDays });
                        }}
                      >
                        {dayLabels[day as keyof typeof dayLabels]}
                      </Badge>
                    );
                  })}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="businessHoursStart">Начало рабочего дня</Label>
                  <Input
                    id="businessHoursStart"
                    type="time"
                    value={settings.businessHours.start}
                    onChange={(e) =>
                      updateSettings({
                        businessHours: { ...settings.businessHours, start: e.target.value },
                      })
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="businessHoursEnd">Конец рабочего дня</Label>
                  <Input
                    id="businessHoursEnd"
                    type="time"
                    value={settings.businessHours.end}
                    onChange={(e) =>
                      updateSettings({
                        businessHours: { ...settings.businessHours, end: e.target.value },
                      })
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
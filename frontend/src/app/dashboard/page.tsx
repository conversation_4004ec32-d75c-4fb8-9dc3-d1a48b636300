"use client";

import { useState, useEffect } from "react";
import { useDashboardStats } from "@/lib/react-query/hooks";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { OnboardingWizard } from "@/components/onboarding/onboarding-wizard";
import { 
  DollarSign, 
  TrendingUp, 
  Users, 
  Building2,
  Activity,
  Target,
  Clock,
  CheckCircle,
  ArrowUpRight,
  BarChart3
} from "lucide-react";

export default function DashboardPage() {
  const { data: stats, isLoading, error } = useDashboardStats();
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    // Check if onboarding has been completed
    const onboardingCompleted = localStorage.getItem("onboardingCompleted");
    if (!onboardingCompleted) {
      setShowOnboarding(true);
    }
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-5 w-96" />
          </div>
        </div>
        
        {/* Bento Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-6 auto-rows-[140px]">
          {/* Main metric - large card */}
          <div className="col-span-2 row-span-2">
            <Skeleton className="w-full h-full rounded-2xl" />
          </div>
          
          {/* Other metrics */}
          {[...Array(6)].map((_, i) => (
            <div key={i} className="col-span-1">
              <Skeleton className="w-full h-full rounded-xl" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-8"
      >
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">Главная</h1>
          <p className="text-muted-foreground mt-2">Добро пожаловать! Вот что происходит с вашими продажами.</p>
        </div>
        <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6">
          <p className="text-destructive font-medium">Не удалось загрузить данные</p>
          <p className="text-destructive/70 text-sm mt-1">Пожалуйста, обновите страницу</p>
        </div>
      </motion.div>
    );
  }

  const mainMetric = {
    title: "Общая сумма воронки",
    value: formatCurrency(stats?.total_value || 0),
    icon: DollarSign,
    description: "По всем активным сделкам",
    change: "+12.5%",
    trend: "up" as const
  };

  const metrics = [
    {
      title: "Всего карточек",
      value: stats?.total_cards?.toString() || "0",
      icon: Target,
      description: "Активные возможности",
      change: "+5.2%",
      trend: "up" as const
    },
    {
      title: "Выигранные сделки",
      value: stats?.won_cards?.toString() || "0",
      icon: CheckCircle,
      description: formatCurrency(stats?.won_value || 0),
      change: "+8.1%",
      trend: "up" as const
    },
    {
      title: "Конверсия",
      value: `${stats?.conversion_rate?.toFixed(1) || "0"}%`,
      icon: TrendingUp,
      description: "Процент успеха",
      change: "+2.3%",
      trend: "up" as const
    },
    {
      title: "Средняя сделка",
      value: formatCurrency((stats?.total_value || 0) / Math.max(stats?.total_cards || 1, 1)),
      icon: BarChart3,
      description: "На возможность",
      change: "-1.2%",
      trend: "down" as const
    },
    {
      title: "Активные контакты",
      value: "247",
      icon: Users,
      description: "В вашей сети",
      change: "+15.3%",
      trend: "up" as const
    },
    {
      title: "Компании",
      value: "89",
      icon: Building2,
      description: "Всего компаний",
      change: "+3.2%",
      trend: "up" as const
    }
  ];

  return (
    <>
      {showOnboarding && (
        <OnboardingWizard onComplete={() => setShowOnboarding(false)} />
      )}
      
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="space-y-8"
      >
      {/* Header */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="text-muted-foreground mt-2">
            Welcome back! Here's what's happening with your sales.
          </p>
        </div>
      </motion.div>

      {/* Bento Grid Layout */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-6 auto-rows-[140px]">
        {/* Main Pipeline Value Card - Large */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="col-span-2 row-span-2"
        >
          <Card className="h-full bg-gradient-to-br from-blue-500 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] group overflow-hidden relative">
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <CardHeader className="pb-2 relative z-10">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold text-white/90">
                  {mainMetric.title}
                </CardTitle>
                <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-4xl font-bold mb-2 text-white">{mainMetric.value}</div>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1 bg-white/20 px-2 py-1 rounded-full backdrop-blur-sm">
                  <ArrowUpRight className="h-3 w-3" />
                  <span className="text-xs font-medium">{mainMetric.change}</span>
                </div>
                <p className="text-sm text-white/80">{mainMetric.description}</p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Other Metrics Cards */}
        {metrics.map((metric, index) => {
          const Icon = metric.icon;
          const isNegative = metric.trend === 'down';
          
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
              className="col-span-1"
            >
              <Card className="h-full bg-card/50 backdrop-blur-sm border border-border/50 hover:bg-card/80 transition-all duration-300 hover:scale-105 hover:shadow-md group">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
                      {metric.title}
                    </CardTitle>
                    <div className="p-2 bg-muted/50 group-hover:bg-primary/10 rounded-lg transition-colors">
                      <Icon className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold mb-1 group-hover:text-primary transition-colors">{metric.value}</div>
                  <div className="flex items-center gap-2">
                    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                      isNegative 
                        ? 'bg-red-100 text-red-600 dark:bg-red-950 dark:text-red-400' 
                        : 'bg-green-100 text-green-600 dark:bg-green-950 dark:text-green-400'
                    }`}>
                      <ArrowUpRight className={`h-3 w-3 ${
                        isNegative ? 'rotate-180' : ''
                      }`} />
                      <span>{metric.change}</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {metric.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Pipeline Stages Overview */}
      {stats?.cards_by_stage && stats.cards_by_stage.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card className="bg-card/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-white" />
                </div>
                Обзор воронки
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.cards_by_stage.map((stage, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.9 + index * 0.1 }}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors group"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 group-hover:scale-110 transition-transform"></div>
                      <span className="font-medium group-hover:text-primary transition-colors">{stage.stage_name}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-lg">{stage.count}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatCurrency(stage.value)}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Recent Activities */}
      {stats?.recent_activities && stats.recent_activities.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <Card className="bg-card/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg">
                  <Activity className="h-5 w-5 text-white" />
                </div>
                Недавние действия
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recent_activities.slice(0, 5).map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.1 + index * 0.1 }}
                    className="flex items-start space-x-4 p-3 rounded-lg hover:bg-muted/50 transition-colors group"
                  >
                    <div className="w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-blue-600 mt-3 group-hover:scale-125 transition-transform"></div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm group-hover:text-primary transition-colors truncate">
                        {activity.title}
                      </p>
                      {activity.description && (
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {activity.description}
                        </p>
                      )}
                      <div className="flex items-center gap-2 mt-2">
                        <p className="text-xs text-muted-foreground">
                          {new Date(activity.created_at).toLocaleDateString()}
                        </p>
                        <span className="w-1 h-1 bg-muted-foreground rounded-full"></span>
                        <p className="text-xs text-muted-foreground">
                          {activity.user?.first_name} {activity.user?.last_name}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </motion.div>
  );
}
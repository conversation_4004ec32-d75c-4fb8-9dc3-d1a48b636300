@import "tailwindcss";
@import "../styles/checkbox-animations.css";

:root {
  /* Enhanced Color System - Light Mode */
  --background: 0 0% 100%;          /* #FFFFFF */
  --foreground: 222 84% 5%;         /* #0F172A - Slate 900 */
  
  --card: 0 0% 100%;                /* #FFFFFF */
  --card-foreground: 222 84% 5%;    /* #0F172A - Slate 900 */
  
  --popover: 0 0% 100%;             /* #FFFFFF */
  --popover-foreground: 222 84% 5%; /* #0F172A - Slate 900 */
  
  /* Updated Primary Brand Colors */
  --primary: 217 87% 56%;           /* #3B82F6 - Blue 500 */
  --primary-foreground: 210 40% 98%; /* #F8FAFC - Slate 50 */
  
  --secondary: 210 40% 95%;         /* #F1F5F9 - Slate 100 */
  --secondary-foreground: 222 84% 5%; /* #0F172A - Slate 900 */
  
  --muted: 210 40% 98%;             /* #F8FAFC - Slate 50 */
  --muted-foreground: 215 16% 47%;  /* #64748B - Slate 500 */
  
  /* Enhanced Accent Color */
  --accent: 142 76% 73%;            /* #86EFAC - Green 300 */
  --accent-foreground: 159 100% 6%; /* #003D16 - Green 950 */
  
  /* Status Colors */
  --destructive: 0 84% 60%;         /* #EF4444 - Red 500 */
  --destructive-foreground: 210 40% 98%;
  --success: 142 71% 45%;           /* #16A34A - Green 600 */
  --warning: 43 96% 56%;            /* #EAB308 - Yellow 500 */
  --info: 201 96% 32%;              /* #0284C7 - Sky 600 */
  
  --border: 214 32% 91%;            /* #E2E8F0 - Slate 200 */
  --input: 214 32% 91%;             /* #E2E8F0 - Slate 200 */
  --ring: 217 87% 56%;              /* #3B82F6 - Blue 500 */
  
  /* Chart Colors */
  --chart-1: 217 87% 56%;           /* Primary Blue */
  --chart-2: 142 71% 45%;           /* Success Green */
  --chart-3: 43 96% 56%;            /* Warning Yellow */
  --chart-4: 281 87% 65%;           /* Purple */
  --chart-5: 201 96% 32%;           /* Info Blue */
  
  --radius: 0.5rem;
}

.dark {
  /* Enhanced Color System - Dark Mode */
  --background: 222 84% 5%;         /* #0F172A - Slate 900 */
  --foreground: 210 40% 98%;        /* #F8FAFC - Slate 50 */
  
  --card: 217 33% 10%;              /* #0F1929 - Darker Slate */
  --card-foreground: 210 40% 98%;   /* #F8FAFC - Slate 50 */
  
  --popover: 217 33% 10%;           /* #0F1929 - Darker Slate */
  --popover-foreground: 210 40% 98%; /* #F8FAFC - Slate 50 */
  
  /* Dark Mode Primary */
  --primary: 217 91% 60%;           /* #60A5FA - Blue 400 */
  --primary-foreground: 222 84% 5%; /* #0F172A - Slate 900 */
  
  --secondary: 217 33% 17%;         /* #1E293B - Slate 800 */
  --secondary-foreground: 210 40% 98%; /* #F8FAFC - Slate 50 */
  
  --muted: 217 33% 17%;             /* #1E293B - Slate 800 */
  --muted-foreground: 215 20% 65%;  /* #94A3B8 - Slate 400 */
  
  /* Dark Mode Accent */
  --accent: 142 71% 45%;            /* #16A34A - Green 600 */
  --accent-foreground: 210 40% 98%; /* #F8FAFC - Slate 50 */
  
  /* Dark Mode Status Colors */
  --destructive: 0 72% 51%;         /* #DC2626 - Red 600 */
  --destructive-foreground: 210 40% 98%;
  --success: 142 76% 36%;           /* #15803D - Green 700 */
  --warning: 43 96% 45%;            /* #CA8A04 - Yellow 600 */
  --info: 201 90% 27%;              /* #0369A1 - Sky 700 */
  
  --border: 217 33% 17%;            /* #1E293B - Slate 800 */
  --input: 217 33% 17%;             /* #1E293B - Slate 800 */
  --ring: 217 91% 60%;              /* #60A5FA - Blue 400 */
  
  /* Dark Mode Chart Colors */
  --chart-1: 217 91% 60%;           /* Primary Blue */
  --chart-2: 142 76% 36%;           /* Success Green */
  --chart-3: 43 96% 45%;            /* Warning Yellow */
  --chart-4: 281 75% 55%;           /* Purple */
  --chart-5: 201 90% 27%;           /* Info Blue */
}

* {
  border-color: hsl(var(--border));
}

/* Typography System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

html {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Scale */
.text-display {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 800;
  letter-spacing: -0.025em;
}

.text-h1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.text-h2 {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.text-h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
}

.text-h4 {
  font-size: 1.125rem;
  line-height: 1.5rem;
  font-weight: 600;
}

.text-body {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
}

.text-body-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
}

.text-caption {
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 400;
}

code, pre {
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced focus states */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Disable transitions on page load */
.preload * {
  transition: none !important;
}

/* Success/Warning/Error States */
.text-success {
  color: hsl(var(--success));
}

.text-warning {
  color: hsl(var(--warning));
}

.text-error {
  color: hsl(var(--destructive));
}

.text-info {
  color: hsl(var(--info));
}

.bg-success {
  background-color: hsl(var(--success));
}

.bg-warning {
  background-color: hsl(var(--warning));
}

.bg-error {
  background-color: hsl(var(--destructive));
}

.bg-info {
  background-color: hsl(var(--info));
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.2);
}

.dark .card-hover:hover {
  box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.5);
}

"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { Search, Building2, User, CreditCard } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  CommandDialog,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { GlobalSearchResult } from "@/types"
import { searchApi } from "@/lib/api"
import { useDebounce } from "@/hooks/use-debounce"

interface GlobalSearchProps {
  className?: string
}

export function GlobalSearch({ className }: GlobalSearchProps) {
  const router = useRouter()
  const [open, setOpen] = React.useState(false)
  const [query, setQuery] = React.useState("")
  const [results, setResults] = React.useState<{
    cards: GlobalSearchResult[]
    contacts: GlobalSearchResult[]
    companies: GlobalSearchResult[]
  }>({
    cards: [],
    contacts: [],
    companies: [],
  })
  const [loading, setLoading] = React.useState(false)

  const debouncedQuery = useDebounce(query, 300)

  // Perform search when debounced query changes
  React.useEffect(() => {
    if (!debouncedQuery.trim()) {
      setResults({ cards: [], contacts: [], companies: [] })
      return
    }

    const performSearch = async () => {
      try {
        setLoading(true)
        const response = await searchApi.globalSearch(debouncedQuery)
        if (response.success && response.data) {
          const { cards = [], contacts = [], companies = [] } = response.data
          setResults({
            cards: cards.map((item: any) => ({
              type: "card" as const,
              id: item.id,
              title: item.title,
              subtitle: item.stage?.name || "No stage",
              metadata: {
                value: item.value,
                currency: item.currency,
                company: item.company?.name,
                contact: item.contact ? `${item.contact.first_name} ${item.contact.last_name}` : null,
                priority: item.priority,
              },
            })),
            contacts: contacts.map((item: any) => ({
              type: "contact" as const,
              id: item.id,
              title: `${item.first_name} ${item.last_name}`,
              subtitle: item.email || "No email",
              metadata: {
                company: item.company?.name,
                job_title: item.job_title,
                phone: item.phone,
              },
            })),
            companies: companies.map((item: any) => ({
              type: "company" as const,
              id: item.id,
              title: item.name,
              subtitle: item.industry || "No industry",
              metadata: {
                size: item.size,
                website: item.website,
                email: item.email,
              },
            })),
          })
        }
      } catch (error) {
        console.error("Search error:", error)
      } finally {
        setLoading(false)
      }
    }

    performSearch()
  }, [debouncedQuery])

  // Handle keyboard shortcut
  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  const handleSelect = (result: GlobalSearchResult) => {
    setOpen(false)
    setQuery("")
    
    // Navigate to the appropriate page
    switch (result.type) {
      case "card":
        // Navigate to pipeline or cards page with card focused
        router.push(`/pipeline?card=${result.id}`)
        break
      case "contact":
        router.push(`/contacts/${result.id}`)
        break
      case "company":
        router.push(`/companies/${result.id}`)
        break
    }
  }

  const getResultIcon = (type: string) => {
    switch (type) {
      case "card":
        return <CreditCard className="h-4 w-4" />
      case "contact":
        return <User className="h-4 w-4" />
      case "company":
        return <Building2 className="h-4 w-4" />
      default:
        return <Search className="h-4 w-4" />
    }
  }

  const getResultColor = (type: string) => {
    switch (type) {
      case "card":
        return "text-blue-600"
      case "contact":
        return "text-green-600"
      case "company":
        return "text-orange-600"
      default:
        return "text-gray-600"
    }
  }

  const formatCurrency = (value: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency || "USD",
    }).format(value)
  }

  const renderCardResult = (result: GlobalSearchResult) => (
    <CommandItem
      key={result.id}
      value={`${result.title} ${result.subtitle} ${result.metadata?.company || ""}`}
      onSelect={() => handleSelect(result)}
      className="flex items-center gap-3 py-3"
    >
      <div className={cn("flex-shrink-0", getResultColor("card"))}>
        {getResultIcon("card")}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="font-medium truncate">{result.title}</span>
          {result.metadata?.priority && (
            <Badge
              variant="secondary"
              className={cn(
                "text-xs",
                result.metadata.priority === "urgent" && "bg-red-100 text-red-800",
                result.metadata.priority === "high" && "bg-orange-100 text-orange-800",
                result.metadata.priority === "medium" && "bg-yellow-100 text-yellow-800",
                result.metadata.priority === "low" && "bg-gray-100 text-gray-800"
              )}
            >
              {result.metadata.priority}
            </Badge>
          )}
        </div>
        <div className="text-sm text-muted-foreground">
          {result.subtitle}
          {result.metadata?.value && (
            <span className="ml-2">
              • {formatCurrency(result.metadata.value, result.metadata.currency)}
            </span>
          )}
          {result.metadata?.company && (
            <span className="ml-2">• {result.metadata.company}</span>
          )}
          {result.metadata?.contact && (
            <span className="ml-2">• {result.metadata.contact}</span>
          )}
        </div>
      </div>
    </CommandItem>
  )

  const renderContactResult = (result: GlobalSearchResult) => (
    <CommandItem
      key={result.id}
      value={`${result.title} ${result.subtitle} ${result.metadata?.company || ""}`}
      onSelect={() => handleSelect(result)}
      className="flex items-center gap-3 py-3"
    >
      <Avatar className="h-8 w-8">
        <AvatarFallback className="text-xs">
          {result.title.split(" ").map(n => n[0]).join("").slice(0, 2)}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{result.title}</div>
        <div className="text-sm text-muted-foreground">
          {result.subtitle}
          {result.metadata?.company && (
            <span className="ml-2">• {result.metadata.company}</span>
          )}
          {result.metadata?.job_title && (
            <span className="ml-2">• {result.metadata.job_title}</span>
          )}
        </div>
      </div>
    </CommandItem>
  )

  const renderCompanyResult = (result: GlobalSearchResult) => (
    <CommandItem
      key={result.id}
      value={`${result.title} ${result.subtitle}`}
      onSelect={() => handleSelect(result)}
      className="flex items-center gap-3 py-3"
    >
      <div className={cn("flex-shrink-0", getResultColor("company"))}>
        {getResultIcon("company")}
      </div>
      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{result.title}</div>
        <div className="text-sm text-muted-foreground">
          {result.subtitle}
          {result.metadata?.size && (
            <span className="ml-2">• {result.metadata.size} employees</span>
          )}
          {result.metadata?.website && (
            <span className="ml-2">• {result.metadata.website}</span>
          )}
        </div>
      </div>
    </CommandItem>
  )

  return (
    <>
      <Button
        variant="outline"
        className={cn(
          "relative h-9 w-full justify-start text-muted-foreground sm:pr-12 md:w-40 lg:w-64",
          className
        )}
        onClick={() => setOpen(true)}
      >
        <Search className="mr-2 h-4 w-4" />
        <span className="hidden lg:inline-flex">Search everything...</span>
        <span className="inline-flex lg:hidden">Search...</span>
        <kbd className="pointer-events-none absolute right-1.5 top-2 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput
          placeholder="Search cards, contacts, companies..."
          value={query}
          onValueChange={setQuery}
        />
        <CommandList>
          <CommandEmpty>
            {loading ? "Searching..." : "No results found."}
          </CommandEmpty>

          {results.cards.length > 0 && (
            <CommandGroup heading="Cards">
              {results.cards.map(renderCardResult)}
            </CommandGroup>
          )}

          {results.contacts.length > 0 && (
            <CommandGroup heading="Contacts">
              {results.contacts.map(renderContactResult)}
            </CommandGroup>
          )}

          {results.companies.length > 0 && (
            <CommandGroup heading="Companies">
              {results.companies.map(renderCompanyResult)}
            </CommandGroup>
          )}
        </CommandList>
      </CommandDialog>
    </>
  )
}
"use client"

import * as React from "react"
import { formatDistanceToNow } from "date-fns"
import { Activity, ActivityType, CreateActivityRequest } from "@/types"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { 
  MessageSquare, 
  Phone, 
  Mail, 
  Calendar, 
  CheckSquare,
  ArrowRightLeft,
  Plus,
  FileText,
  Building2,
  <PERSON><PERSON>,
  <PERSON><PERSON>2
} from "lucide-react"
import { cn } from "@/lib/utils"
import { activitiesApi } from "@/lib/api/activities"
import { useMutation, useQueryClient } from "@tanstack/react-query"

const activitySchema = z.object({
  type: z.enum(["note", "email", "call", "meeting", "task"]),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
})

type ActivityFormData = z.infer<typeof activitySchema>

interface ActivityTimelineProps {
  activities: Activity[]
  entityId?: string
  entityType?: "card" | "contact" | "company"
  onActivityAdded?: () => void
  className?: string
  showAddForm?: boolean
}

const activityTypeIcons: Record<ActivityType, React.ReactNode> = {
  note: <MessageSquare className="h-4 w-4" />,
  email: <Mail className="h-4 w-4" />,
  call: <Phone className="h-4 w-4" />,
  meeting: <Calendar className="h-4 w-4" />,
  task: <CheckSquare className="h-4 w-4" />,
  stage_changed: <ArrowRightLeft className="h-4 w-4" />,
  card_created: <Plus className="h-4 w-4" />,
  card_updated: <FileText className="h-4 w-4" />,
  contact_created: <User className="h-4 w-4" />,
  contact_updated: <User className="h-4 w-4" />,
  company_created: <Building2 className="h-4 w-4" />,
  company_updated: <Building2 className="h-4 w-4" />,
}

const activityTypeLabels: Record<ActivityType, string> = {
  note: "Note",
  email: "Email",
  call: "Call",
  meeting: "Meeting", 
  task: "Task",
  stage_changed: "Stage Changed",
  card_created: "Card Created",
  card_updated: "Card Updated",
  contact_created: "Contact Created",
  contact_updated: "Contact Updated",
  company_created: "Company Created",
  company_updated: "Company Updated",
}

const getActivityTypeColor = (type: ActivityType): string => {
  switch (type) {
    case "note": return "text-blue-600 bg-blue-50"
    case "email": return "text-purple-600 bg-purple-50"
    case "call": return "text-green-600 bg-green-50"
    case "meeting": return "text-orange-600 bg-orange-50"
    case "task": return "text-red-600 bg-red-50"
    case "stage_changed": return "text-indigo-600 bg-indigo-50"
    case "card_created":
    case "card_updated": return "text-cyan-600 bg-cyan-50"
    case "contact_created":
    case "contact_updated": return "text-emerald-600 bg-emerald-50"
    case "company_created":
    case "company_updated": return "text-yellow-600 bg-yellow-50"
    default: return "text-gray-600 bg-gray-50"
  }
}

export function ActivityTimeline({
  activities,
  entityId,
  entityType,
  onActivityAdded,
  className,
  showAddForm = true,
}: ActivityTimelineProps) {
  const [showForm, setShowForm] = React.useState(false)
  const queryClient = useQueryClient()

  const form = useForm<ActivityFormData>({
    resolver: zodResolver(activitySchema),
    defaultValues: {
      type: "note",
      title: "",
      description: "",
    },
  })

  const createActivityMutation = useMutation({
    mutationFn: (data: CreateActivityRequest) => activitiesApi.createActivity(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["activities"] })
      onActivityAdded?.()
      form.reset()
      setShowForm(false)
    },
    onError: (error) => {
      console.error("Error creating activity:", error)
    },
  })

  const onSubmit = (data: ActivityFormData) => {
    if (!entityId || !entityType) return

    const createData: CreateActivityRequest = {
      ...data,
      [`${entityType}_id`]: entityId,
    }

    createActivityMutation.mutate(createData)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {showAddForm && (
        <Card>
          <CardContent className="pt-4">
            {!showForm ? (
              <Button
                onClick={() => setShowForm(true)}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Activity
              </Button>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Add New Activity</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setShowForm(false)
                      form.reset()
                    }}
                  >
                    Cancel
                  </Button>
                </div>
                
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <FormField
                        control={form.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Type</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="note">Note</SelectItem>
                                <SelectItem value="email">Email</SelectItem>
                                <SelectItem value="call">Call</SelectItem>
                                <SelectItem value="meeting">Meeting</SelectItem>
                                <SelectItem value="task">Task</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Title</FormLabel>
                            <FormControl>
                              <Input placeholder="Activity title" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Activity description..." 
                              className="min-h-[60px]"
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button 
                      type="submit" 
                      size="sm" 
                      disabled={createActivityMutation.isPending}
                      className="w-full"
                    >
                      {createActivityMutation.isPending && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Add Activity
                    </Button>
                  </form>
                </Form>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {activities.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No activities yet</p>
        </div>
      ) : (
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-6 top-0 bottom-0 w-px bg-border" />
          
          <div className="space-y-4">
            {activities.map((activity, index) => (
              <div key={activity.id} className="relative flex gap-4">
                {/* Timeline dot */}
                <div className={cn(
                  "relative z-10 flex h-12 w-12 items-center justify-center rounded-full border-2 border-white shadow-sm",
                  getActivityTypeColor(activity.type)
                )}>
                  {activityTypeIcons[activity.type]}
                </div>

                {/* Activity content */}
                <div className="flex-1 pb-4">
                  <Card>
                    <CardContent className="pt-4">
                      <div className="space-y-2">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary" className="text-xs">
                                {activityTypeLabels[activity.type]}
                              </Badge>
                              <h4 className="text-sm font-medium">
                                {activity.title}
                              </h4>
                            </div>
                            {activity.description && (
                              <p className="text-sm text-muted-foreground">
                                {activity.description}
                              </p>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <div className="flex items-center gap-2">
                            {activity.user && (
                              <div className="flex items-center gap-1">
                                <Avatar className="h-4 w-4">
                                  <AvatarFallback className="text-xs">
                                    {activity.user.first_name?.[0]}{activity.user.last_name?.[0]}
                                  </AvatarFallback>
                                </Avatar>
                                <span>
                                  {activity.user.first_name} {activity.user.last_name}
                                </span>
                              </div>
                            )}
                          </div>
                          <span>
                            {formatDistanceToNow(new Date(activity.created_at))} ago
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
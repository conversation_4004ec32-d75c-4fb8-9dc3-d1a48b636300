import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "./keys";
import {
  authApi,
  cardsApi,
  pipelinesApi,
  contactsApi,
  companiesApi,
  fieldsApi,
  dashboardApi,
  tagsApi,
  searchApi,
} from "@/lib/api";
import { useNotifications } from "@/stores";
import { 
  CardFilters, 
  SearchParams, 
  CreateCardRequest, 
  UpdateCardRequest, 
  MoveCardRequest,
  CreateContactRequest,
  CreateCompanyRequest,
} from "@/types";

// Auth hooks
export const useCurrentUser = () => {
  return useQuery({
    queryKey: queryKeys.auth.currentUser,
    queryFn: () => authApi.getCurrentUser().then(res => res.data),
  });
};

// Pipeline hooks
export const usePipelines = () => {
  return useQuery({
    queryKey: queryKeys.pipelines.all,
    queryFn: () => pipelinesApi.getPipelines().then(res => res.data?.pipelines || []),
  });
};

export const usePipeline = (id: string) => {
  return useQuery({
    queryKey: queryKeys.pipelines.pipeline(id),
    queryFn: () => pipelinesApi.getPipeline(id).then(res => res.data),
    enabled: !!id,
  });
};

export const usePipelineStages = (pipelineId: string) => {
  return useQuery({
    queryKey: queryKeys.pipelines.stages(pipelineId),
    queryFn: () => pipelinesApi.getStages(pipelineId).then(res => res.data),
    enabled: !!pipelineId,
  });
};

// Card hooks
export const useCards = (filters?: CardFilters) => {
  return useQuery({
    queryKey: queryKeys.cards.all(filters),
    queryFn: () => cardsApi.getCards(filters).then(res => res.data),
  });
};

export const useCard = (id: string) => {
  return useQuery({
    queryKey: queryKeys.cards.card(id),
    queryFn: () => cardsApi.getCard(id).then(res => res.data),
    enabled: !!id,
  });
};

export const useCardActivities = (cardId: string) => {
  return useQuery({
    queryKey: queryKeys.cards.activities(cardId),
    queryFn: () => cardsApi.getCardActivities(cardId).then(res => res.data),
    enabled: !!cardId,
  });
};

// Card mutations
export const useCreateCard = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (data: CreateCardRequest) => cardsApi.createCard(data),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries({ queryKey: queryKeys.cards.all() });
        notifications.success("Card created", "The card has been created successfully.");
      } else {
        notifications.error("Failed to create card", response.message);
      }
    },
    onError: (error: any) => {
      notifications.error("Failed to create card", error.response?.data?.message);
    },
  });
};

export const useUpdateCard = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (data: UpdateCardRequest) => cardsApi.updateCard(data),
    onSuccess: (response, variables) => {
      if (response.success) {
        queryClient.invalidateQueries({ queryKey: queryKeys.cards.card(variables.id) });
        queryClient.invalidateQueries({ queryKey: queryKeys.cards.all() });
        notifications.success("Card updated", "The card has been updated successfully.");
      } else {
        notifications.error("Failed to update card", response.message);
      }
    },
    onError: (error: any) => {
      notifications.error("Failed to update card", error.response?.data?.message);
    },
  });
};

export const useMoveCard = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (data: MoveCardRequest) => cardsApi.moveCard(data),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries({ queryKey: queryKeys.cards.all() });
      } else {
        notifications.error("Failed to move card", response.message);
      }
    },
    onError: (error: any) => {
      notifications.error("Failed to move card", error.response?.data?.message);
    },
  });
};

export const useDeleteCard = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (id: string) => cardsApi.deleteCard(id),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries({ queryKey: queryKeys.cards.all() });
        notifications.success("Card deleted", "The card has been deleted successfully.");
      } else {
        notifications.error("Failed to delete card", response.message);
      }
    },
    onError: (error: any) => {
      notifications.error("Failed to delete card", error.response?.data?.message);
    },
  });
};

// Contact hooks
export const useContacts = (params?: SearchParams) => {
  return useQuery({
    queryKey: queryKeys.contacts.all(params),
    queryFn: () => contactsApi.getContacts(params).then(res => res.data),
    keepPreviousData: true,
    staleTime: 5000,
  });
};

export const useContact = (id: string) => {
  return useQuery({
    queryKey: queryKeys.contacts.contact(id),
    queryFn: () => contactsApi.getContact(id).then(res => res.data),
    enabled: !!id,
  });
};

// Contact mutations
export const useCreateContact = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (data: CreateContactRequest) => contactsApi.createContact(data),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries({ queryKey: queryKeys.contacts.all() });
        notifications.success("Contact created", "The contact has been created successfully.");
      } else {
        notifications.error("Failed to create contact", response.message);
      }
    },
    onError: (error: any) => {
      notifications.error("Failed to create contact", error.response?.data?.message);
    },
  });
};

// Company hooks
export const useCompanies = (params?: SearchParams) => {
  return useQuery({
    queryKey: queryKeys.companies.all(params),
    queryFn: () => companiesApi.getCompanies(params).then(res => res.data),
    keepPreviousData: true,
    staleTime: 5000,
  });
};

export const useCompany = (id: string) => {
  return useQuery({
    queryKey: queryKeys.companies.company(id),
    queryFn: () => companiesApi.getCompany(id).then(res => res.data),
    enabled: !!id,
  });
};

// Company mutations
export const useCreateCompany = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (data: CreateCompanyRequest) => companiesApi.createCompany(data),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries({ queryKey: queryKeys.companies.all() });
        notifications.success("Company created", "The company has been created successfully.");
      } else {
        notifications.error("Failed to create company", response.message);
      }
    },
    onError: (error: any) => {
      notifications.error("Failed to create company", error.response?.data?.message);
    },
  });
};

// Field Definition hooks
export const useFieldDefinitions = (entityType?: string) => {
  return useQuery({
    queryKey: queryKeys.fieldDefinitions.all(entityType),
    queryFn: () => fieldsApi.getFieldDefinitions(entityType as any).then(res => res.data),
  });
};

// Tags hooks
export const useTags = (entityType?: string) => {
  return useQuery({
    queryKey: queryKeys.tags.all(entityType),
    queryFn: () => tagsApi.getTags(entityType as any).then(res => res.data),
  });
};

// Dashboard hooks
export const useDashboardStats = (dateFrom?: string, dateTo?: string) => {
  return useQuery({
    queryKey: queryKeys.dashboard.stats(dateFrom, dateTo),
    queryFn: () => dashboardApi.getDashboardStats(dateFrom, dateTo).then(res => res.data),
  });
};

// Search hooks
export const useGlobalSearch = (query: string, entityTypes?: string[]) => {
  return useQuery({
    queryKey: queryKeys.search.global(query, entityTypes),
    queryFn: () => searchApi.globalSearch(query, entityTypes as any).then(res => res.data),
    enabled: query.length >= 2, // Only search when query is at least 2 characters
  });
};